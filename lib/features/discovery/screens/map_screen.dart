import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:geolocator/geolocator.dart';

import '../../../core/services/location_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../business/domain/business_summary_model.dart';
import '../../business/data/business_repository.dart';
import '../../../shared/widgets/enhanced_search_bar.dart';
import '../widgets/map_business_card.dart';
import '../widgets/map_filter_sheet.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  final LocationService _locationService = LocationService.instance;
  
  Set<Marker> _markers = {};
  List<BusinessSummaryModel> _businesses = [];
  BusinessSummaryModel? _selectedBusiness;
  bool _isLoading = true;
  String? _errorMessage;
  
  // Map settings
  CameraPosition _initialPosition = const CameraPosition(
    target: LatLng(37.7749, -122.4194), // Default to San Francisco
    zoom: 14.0,
  );
  
  // Search and filter
  String _searchQuery = '';
  double _searchRadius = 10.0; // km
  bool _showSearchOverlay = false;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    try {
      // Get user location
      final position = await _locationService.getCurrentPosition();
      
      if (position != null) {
        setState(() {
          _initialPosition = CameraPosition(
            target: LatLng(position.latitude, position.longitude),
            zoom: 14.0,
          );
        });
        
        // Load nearby businesses
        await _loadNearbyBusinesses(position.latitude, position.longitude);
      } else {
        // Load businesses for default location
        await _loadNearbyBusinesses(37.7749, -122.4194);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load map: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadNearbyBusinesses(double lat, double lng) async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final businesses = await context.read<BusinessRepository>().getBusinesses(
        latitude: lat,
        longitude: lng,
        query: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _businesses = businesses;
        _isLoading = false;
      });

      await _updateMarkers();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load businesses: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _updateMarkers() async {
    final markers = <Marker>{};

    for (final business in _businesses) {
      if (business.latitude != null && business.longitude != null) {
        markers.add(
          Marker(
            markerId: MarkerId(business.id.toString()),
            position: LatLng(business.latitude!, business.longitude!),
            infoWindow: InfoWindow(
              title: business.name,
              snippet: '${business.averageRating.toStringAsFixed(1)} ⭐ • ${business.priceRange}',
            ),
            icon: await _getMarkerIcon(business),
            onTap: () => _onMarkerTapped(business),
          ),
        );
      }
    }

    setState(() {
      _markers = markers;
    });
  }

  Future<BitmapDescriptor> _getMarkerIcon(BusinessSummaryModel business) async {
    // Create custom marker based on business category or rating
    if (business.averageRating >= 4.5) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else if (business.averageRating >= 4.0) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    } else {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    }
  }

  void _onMarkerTapped(BusinessSummaryModel business) {
    setState(() {
      _selectedBusiness = business;
    });
  }

  Future<void> _onMapTapped(LatLng position) async {
    setState(() {
      _selectedBusiness = null;
    });
  }

  Future<void> _onCameraMove(CameraPosition position) async {
    // Optionally load businesses for new area when camera stops moving
  }

  Future<void> _centerOnUserLocation() async {
    final position = await _locationService.getCurrentPosition(forceRefresh: true);
    if (position != null) {
      final controller = await _controller.future;
      await controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(position.latitude, position.longitude),
            zoom: 16.0,
          ),
        ),
      );
      
      // Load businesses for new location
      await _loadNearbyBusinesses(position.latitude, position.longitude);
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    
    // Debounce search
    Timer(const Duration(milliseconds: 500), () {
      if (_searchQuery == query) {
        _performSearch();
      }
    });
  }

  Future<void> _performSearch() async {
    final position = _locationService.lastKnownPosition;
    if (position != null) {
      await _loadNearbyBusinesses(position.latitude, position.longitude);
    }
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MapFilterSheet(
        currentRadius: _searchRadius,
        onRadiusChanged: (radius) {
          setState(() {
            _searchRadius = radius;
          });
          _performSearch();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: (GoogleMapController controller) {
              _controller.complete(controller);
            },
            initialCameraPosition: _initialPosition,
            markers: _markers,
            onTap: _onMapTapped,
            onCameraMove: _onCameraMove,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            compassEnabled: true,
            trafficEnabled: false,
            buildingsEnabled: true,
            indoorViewEnabled: true,
          ),

          // Search overlay
          if (_showSearchOverlay)
            Container(
              color: AppTheme.white.withOpacity(0.95),
              child: SafeArea(
                child: Column(
                  children: [
                    // Search suggestions or results
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(AppTheme.lg),
                        child: const Center(
                          child: Text('Search suggestions will go here'),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Top overlay with search bar
          SafeArea(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.all(AppTheme.lg),
                  child: EnhancedSearchBar(
                    hintText: 'Search businesses on map...',
                    onChanged: _onSearchChanged,
                    onFilterTap: _showFilterSheet,
                    showFilterButton: true,
                  ),
                ),
              ],
            ),
          ),

          // Bottom overlay with selected business
          if (_selectedBusiness != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: MapBusinessCard(
                business: _selectedBusiness!,
                onClose: () => setState(() => _selectedBusiness = null),
              ),
            ),

          // Floating action buttons
          Positioned(
            bottom: _selectedBusiness != null ? 200 : 100,
            right: AppTheme.lg,
            child: Column(
              children: [
                // My location button
                FloatingActionButton(
                  heroTag: 'location',
                  onPressed: _centerOnUserLocation,
                  backgroundColor: AppTheme.white,
                  foregroundColor: AppTheme.primaryPurple,
                  child: const Icon(FeatherIcons.navigation),
                ),
                const SizedBox(height: AppTheme.sm),
                // List view toggle
                FloatingActionButton(
                  heroTag: 'list',
                  onPressed: () {
                    // Navigate to list view with current businesses
                    Navigator.of(context).pushNamed('/search', arguments: {
                      'businesses': _businesses,
                      'query': _searchQuery,
                    });
                  },
                  backgroundColor: AppTheme.white,
                  foregroundColor: AppTheme.primaryPurple,
                  child: const Icon(FeatherIcons.list),
                ),
              ],
            ),
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: AppTheme.white.withOpacity(0.8),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.primaryPurple,
                ),
              ),
            ),

          // Error overlay
          if (_errorMessage != null)
            Container(
              color: AppTheme.white.withOpacity(0.9),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.xxxl),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        FeatherIcons.alertCircle,
                        size: 64,
                        color: AppTheme.errorRed,
                      ),
                      const SizedBox(height: AppTheme.lg),
                      Text(
                        'Map Error',
                        style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
                      ),
                      const SizedBox(height: AppTheme.sm),
                      Text(
                        _errorMessage!,
                        style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppTheme.lg),
                      ElevatedButton(
                        onPressed: _initializeMap,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
