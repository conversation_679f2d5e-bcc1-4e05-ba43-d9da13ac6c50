import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';
import 'package:geolocator/geolocator.dart';

import '../domain/category_model.dart';
import '../../business/domain/business_summary_model.dart';
import '../../business/data/business_repository.dart';
import '../../../shared/widgets/category_chip.dart';
import '../../../shared/widgets/enhanced_business_card.dart';
import '../../../shared/widgets/enhanced_search_bar.dart';
import '../../../core/theme/app_theme.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // App Bar
            SliverAppBar(
              floating: true,
              backgroundColor: AppTheme.white,
              elevation: 0,
              surfaceTintColor: Colors.transparent,
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'LocalFind',
                    style: AppTheme.h2.copyWith(color: AppTheme.gray900),
                  ),
                  Text(
                    'Discover local businesses near you',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.gray500,
                    ),
                  ),
                ],
              ),
            ),

            // Search Bar
            SliverToBoxAdapter(
              child: SearchBarPlaceholder(
                hintText: 'Search businesses...',
                onTap: () => context.go('/search'),
              ),
            ),

            // Categories Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
                child: Text(
                  'Categories',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
              ),
            ),

            const SliverToBoxAdapter(child: SizedBox(height: AppTheme.lg)),

            // Categories List
            SliverToBoxAdapter(
              child: SizedBox(
                height: 40,
                child: FutureProvider<List<CategoryModel>>(
                  create: (context) =>
                      context.read<BusinessRepository>().getCategories(),
                  initialData: const [],
                  child: Consumer<List<CategoryModel>>(
                    builder: (context, categories, child) {
                      if (categories.isEmpty) {
                        return _buildCategoriesShimmer();
                      }

                      return CategoryChipList(
                        categories: categories,
                        selectedCategoryIds:
                            const [], // No selection on home screen
                        onCategoryTap: (categoryId) {
                          // Navigate to search with category filter
                          context.go('/search?category=$categoryId');
                        },
                      );
                    },
                  ),
                ),
              ),
            ),

            const SliverToBoxAdapter(child: SizedBox(height: 24)),

            // Popular Near You Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
                child: Text(
                  'Popular Near You',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
              ),
            ),

            const SliverToBoxAdapter(child: SizedBox(height: AppTheme.lg)),

            // Nearby Businesses List
            FutureProvider<List<BusinessSummaryModel>>(
              create: (context) => _getNearbyBusinesses(context),
              initialData: const [],
              child: Consumer<List<BusinessSummaryModel>>(
                builder: (context, businesses, child) {
                  if (businesses.isEmpty) {
                    return SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) => _buildBusinessShimmer(),
                        childCount: 5,
                      ),
                    );
                  }

                  return SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      return EnhancedBusinessCard(business: businesses[index]);
                    }, childCount: businesses.length),
                  );
                },
              ),
            ),

            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Future<List<BusinessSummaryModel>> _getNearbyBusinesses(
    BuildContext context,
  ) async {
    final businessRepository = context.read<BusinessRepository>();

    try {
      // Get current location
      final position = await _getCurrentPosition();

      // Fetch nearby businesses
      return await businessRepository.getNearbyBusinesses(
        latitude: position.latitude,
        longitude: position.longitude,
        radius: 10,
        limit: 10,
      );
    } catch (e) {
      // If location fails, just get general businesses
      return await businessRepository.getBusinesses(limit: 10);
    }
  }

  Future<Position> _getCurrentPosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    return await Geolocator.getCurrentPosition();
  }

  Widget _buildCategoriesShimmer() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(right: AppTheme.sm),
          child: Shimmer.fromColors(
            baseColor: AppTheme.gray300,
            highlightColor: AppTheme.gray100,
            child: Container(
              width: 100,
              height: 32,
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(AppTheme.xl),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBusinessShimmer() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
      child: Shimmer.fromColors(
        baseColor: AppTheme.gray300,
        highlightColor: AppTheme.gray100,
        child: Container(
          height: 120,
          decoration: BoxDecoration(
            color: AppTheme.white,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
        ),
      ),
    );
  }
}
