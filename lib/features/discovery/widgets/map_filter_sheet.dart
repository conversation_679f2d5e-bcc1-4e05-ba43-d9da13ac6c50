import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/filter_chips.dart' as custom_chips;

class MapFilterSheet extends StatefulWidget {
  final double currentRadius;
  final Function(double) onRadiusChanged;

  const MapFilterSheet({
    super.key,
    required this.currentRadius,
    required this.onRadiusChanged,
  });

  @override
  State<MapFilterSheet> createState() => _MapFilterSheetState();
}

class _MapFilterSheetState extends State<MapFilterSheet> {
  late double _radius;

  @override
  void initState() {
    super.initState();
    _radius = widget.currentRadius;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Map Filters',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(FeatherIcons.x, color: AppTheme.gray500),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSearchRadiusSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildQuickFiltersSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildMapOptionsSection(),
                ],
              ),
            ),
          ),

          // Apply button
          Container(
            padding: const EdgeInsets.all(AppTheme.lg),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: AppTheme.gray300)),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  widget.onRadiusChanged(_radius);
                  Navigator.of(context).pop();
                },
                child: const Text('Apply Filters'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchRadiusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Radius',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),

        // Radius slider
        Container(
          padding: const EdgeInsets.all(AppTheme.lg),
          decoration: BoxDecoration(
            color: AppTheme.gray100,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Within ${_radius.toStringAsFixed(0)} km',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.gray900,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Icon(
                    FeatherIcons.mapPin,
                    color: AppTheme.distanceBlue,
                    size: 20,
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.sm),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppTheme.distanceBlue,
                  inactiveTrackColor: AppTheme.gray300,
                  thumbColor: AppTheme.distanceBlue,
                  overlayColor: AppTheme.distanceBlue.withOpacity(0.2),
                  trackHeight: 4,
                ),
                child: Slider(
                  value: _radius,
                  min: 1,
                  max: 50,
                  divisions: 49,
                  onChanged: (value) {
                    setState(() {
                      _radius = value;
                    });
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '1 km',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                  ),
                  Text(
                    '50 km',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: AppTheme.md),

        // Quick radius options
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [1, 5, 10, 25].map((km) {
            final isSelected = _radius == km.toDouble();
            return custom_chips.FilterChip(
              label: '${km}km',
              isSelected: isSelected,
              selectedColor: AppTheme.distanceBlue,
              onTap: () {
                setState(() {
                  _radius = km.toDouble();
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Filters',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [
            custom_chips.FilterChip(
              label: 'Open Now',
              icon: FeatherIcons.clock,
              selectedColor: AppTheme.successGreen,
              onTap: () {
                // TODO: Implement open now filter
              },
            ),
            custom_chips.FilterChip(
              label: 'Highly Rated',
              icon: FeatherIcons.star,
              selectedColor: AppTheme.goldStar,
              onTap: () {
                // TODO: Implement rating filter
              },
            ),
            custom_chips.FilterChip(
              label: 'Nearby',
              icon: FeatherIcons.navigation,
              selectedColor: AppTheme.distanceBlue,
              onTap: () {
                setState(() {
                  _radius = 5.0;
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMapOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Map Options',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),

        _buildOptionTile(
          icon: FeatherIcons.layers,
          title: 'Show Traffic',
          subtitle: 'Display real-time traffic information',
          value: false,
          onChanged: (value) {
            // TODO: Implement traffic toggle
          },
        ),

        _buildOptionTile(
          icon: FeatherIcons.home,
          title: 'Show Buildings',
          subtitle: 'Display 3D buildings on the map',
          value: true,
          onChanged: (value) {
            // TODO: Implement buildings toggle
          },
        ),

        _buildOptionTile(
          icon: FeatherIcons.compass,
          title: 'Show Compass',
          subtitle: 'Display compass on the map',
          value: true,
          onChanged: (value) {
            // TODO: Implement compass toggle
          },
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.sm),
      child: ListTile(
        leading: Icon(icon, color: AppTheme.gray700, size: 20),
        title: Text(
          title,
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
        ),
        subtitle: Text(
          subtitle,
          style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: AppTheme.primaryPurple,
        ),
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }
}
