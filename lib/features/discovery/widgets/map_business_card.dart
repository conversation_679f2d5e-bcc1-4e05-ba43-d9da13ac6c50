import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../business/domain/business_summary_model.dart';
import '../../../core/theme/app_theme.dart';

class MapBusinessCard extends StatelessWidget {
  final BusinessSummaryModel business;
  final VoidCallback onClose;

  const MapBusinessCard({
    super.key,
    required this.business,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.lg),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with close button
          Padding(
            padding: const EdgeInsets.only(
              top: AppTheme.sm,
              right: AppTheme.sm,
            ),
            child: Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(
                    FeatherIcons.x,
                    color: AppTheme.gray500,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
          
          // Business content
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.lg,
              0,
              AppTheme.lg,
              AppTheme.lg,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Business image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppTheme.sm),
                      child: SizedBox(
                        width: 60,
                        height: 60,
                        child: business.logoUrl != null
                            ? CachedNetworkImage(
                                imageUrl: business.logoUrl!,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: AppTheme.gray100,
                                  child: const Icon(
                                    FeatherIcons.briefcase,
                                    color: AppTheme.gray500,
                                    size: 24,
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: AppTheme.gray100,
                                  child: const Icon(
                                    FeatherIcons.briefcase,
                                    color: AppTheme.gray500,
                                    size: 24,
                                  ),
                                ),
                              )
                            : Container(
                                color: AppTheme.gray100,
                                child: const Icon(
                                  FeatherIcons.briefcase,
                                  color: AppTheme.gray500,
                                  size: 24,
                                ),
                              ),
                      ),
                    ),
                    
                    const SizedBox(width: AppTheme.md),
                    
                    // Business info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Business name
                          Text(
                            business.name,
                            style: AppTheme.h5.copyWith(color: AppTheme.gray900),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: AppTheme.xs),
                          
                          // Rating and price
                          Row(
                            children: [
                              RatingBarIndicator(
                                rating: business.averageRating,
                                itemBuilder: (context, index) => const Icon(
                                  Icons.star,
                                  color: AppTheme.goldStar,
                                ),
                                itemCount: 5,
                                itemSize: 12,
                              ),
                              const SizedBox(width: AppTheme.xs),
                              Text(
                                '${business.averageRating.toStringAsFixed(1)}',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.gray700,
                                ),
                              ),
                              const SizedBox(width: AppTheme.sm),
                              Text(
                                business.priceRange,
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppTheme.gray700,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: AppTheme.xs),
                          
                          // Distance and status
                          Row(
                            children: [
                              if (business.distanceKm != null) ...[
                                const Icon(
                                  FeatherIcons.navigation,
                                  size: 12,
                                  color: AppTheme.distanceBlue,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${business.distanceKm!.toStringAsFixed(1)} km',
                                  style: AppTheme.bodySmall.copyWith(
                                    color: AppTheme.distanceBlue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: AppTheme.sm),
                              ],
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppTheme.xs,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: business.isOpenNow 
                                      ? AppTheme.successGreen 
                                      : AppTheme.errorRed,
                                  borderRadius: BorderRadius.circular(AppTheme.xs),
                                ),
                                child: Text(
                                  business.isOpenNow ? 'Open' : 'Closed',
                                  style: AppTheme.caption.copyWith(
                                    color: AppTheme.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppTheme.lg),
                
                // Action buttons
                Row(
                  children: [
                    // Call button
                    if (business.phoneNumber != null)
                      Expanded(
                        child: _ActionButton(
                          icon: FeatherIcons.phone,
                          label: 'Call',
                          onTap: () => _makePhoneCall(business.phoneNumber!),
                        ),
                      ),
                    
                    if (business.phoneNumber != null)
                      const SizedBox(width: AppTheme.sm),
                    
                    // Directions button
                    Expanded(
                      child: _ActionButton(
                        icon: FeatherIcons.navigation,
                        label: 'Directions',
                        onTap: () => _openDirections(business),
                      ),
                    ),
                    
                    const SizedBox(width: AppTheme.sm),
                    
                    // View details button
                    Expanded(
                      child: _ActionButton(
                        icon: FeatherIcons.info,
                        label: 'Details',
                        isPrimary: true,
                        onTap: () => context.go('/business/${business.slug}'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  Future<void> _openDirections(BusinessSummaryModel business) async {
    if (business.latitude != null && business.longitude != null) {
      final uri = Uri(
        scheme: 'https',
        host: 'maps.google.com',
        path: '/maps',
        queryParameters: {
          'daddr': '${business.latitude},${business.longitude}',
          'directionsmode': 'driving',
        },
      );
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    }
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isPrimary;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.sm,
          vertical: AppTheme.sm,
        ),
        decoration: BoxDecoration(
          color: isPrimary ? AppTheme.primaryPurple : AppTheme.gray100,
          borderRadius: BorderRadius.circular(AppTheme.sm),
          border: isPrimary 
              ? null 
              : Border.all(color: AppTheme.gray300),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: isPrimary ? AppTheme.white : AppTheme.gray700,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: AppTheme.caption.copyWith(
                color: isPrimary ? AppTheme.white : AppTheme.gray700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
