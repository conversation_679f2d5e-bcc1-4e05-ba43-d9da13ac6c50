import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/location_service.dart';
import '../screens/onboarding_screen.dart';

class OnboardingPage extends StatefulWidget {
  final OnboardingPageData data;
  final Function(bool)? onLocationPermissionChanged;

  const OnboardingPage({
    super.key,
    required this.data,
    this.onLocationPermissionChanged,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  bool _isRequestingPermission = false;
  bool _permissionGranted = false;
  String? _permissionStatus;

  @override
  void initState() {
    super.initState();
    if (widget.data.isLocationPage) {
      _checkLocationStatus();
    }
  }

  Future<void> _checkLocationStatus() async {
    final locationService = LocationService.instance;
    final hasPermission = await locationService.checkLocationStatus();
    
    setState(() {
      _permissionGranted = hasPermission;
      _permissionStatus = locationService.getPermissionStatusText();
    });
    
    widget.onLocationPermissionChanged?.call(_permissionGranted);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Column(
        children: [
          const Spacer(),
          
          // Icon/Image
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.primaryPurple.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              widget.data.icon,
              size: 60,
              color: AppTheme.primaryPurple,
            ),
          ),
          
          const SizedBox(height: AppTheme.xxxl),
          
          // Title
          Text(
            widget.data.title,
            style: AppTheme.h2.copyWith(
              color: AppTheme.gray900,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppTheme.lg),
          
          // Description
          Text(
            widget.data.description,
            style: AppTheme.bodyLarge.copyWith(
              color: AppTheme.gray600,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppTheme.xxxl),
          
          // Location-specific content
          if (widget.data.isLocationPage) ...[
            _buildLocationContent(),
          ],
          
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildLocationContent() {
    return Column(
      children: [
        // Permission status
        if (_permissionStatus != null)
          Container(
            padding: const EdgeInsets.all(AppTheme.md),
            decoration: BoxDecoration(
              color: _permissionGranted 
                  ? AppTheme.successGreen.withOpacity(0.1)
                  : AppTheme.warningOrange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.md),
              border: Border.all(
                color: _permissionGranted 
                    ? AppTheme.successGreen.withOpacity(0.3)
                    : AppTheme.warningOrange.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _permissionGranted ? FeatherIcons.checkCircle : FeatherIcons.alertCircle,
                  color: _permissionGranted ? AppTheme.successGreen : AppTheme.warningOrange,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.sm),
                Expanded(
                  child: Text(
                    _permissionGranted 
                        ? 'Location permission granted'
                        : 'Location permission: $_permissionStatus',
                    style: AppTheme.bodyMedium.copyWith(
                      color: _permissionGranted ? AppTheme.successGreen : AppTheme.warningOrange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        
        const SizedBox(height: AppTheme.lg),
        
        // Enable location button
        if (!_permissionGranted)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isRequestingPermission ? null : _requestLocationPermission,
              icon: _isRequestingPermission
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        color: AppTheme.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(FeatherIcons.mapPin, size: 18),
              label: Text(_isRequestingPermission ? 'Requesting...' : 'Enable Location'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.lg),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.md),
                ),
              ),
            ),
          ),
        
        const SizedBox(height: AppTheme.md),
        
        // Benefits list
        _buildBenefitsList(),
        
        const SizedBox(height: AppTheme.lg),
        
        // Privacy note
        Container(
          padding: const EdgeInsets.all(AppTheme.md),
          decoration: BoxDecoration(
            color: AppTheme.gray100,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
          child: Row(
            children: [
              const Icon(
                FeatherIcons.shield,
                color: AppTheme.gray600,
                size: 16,
              ),
              const SizedBox(width: AppTheme.sm),
              Expanded(
                child: Text(
                  'Your location data is only used to show nearby businesses and is never shared with third parties.',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.gray600,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBenefitsList() {
    final benefits = [
      'Find businesses near you',
      'Get accurate directions',
      'See distance to locations',
      'Discover local recommendations',
    ];

    return Column(
      children: benefits.map((benefit) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppTheme.sm),
          child: Row(
            children: [
              const Icon(
                FeatherIcons.check,
                color: AppTheme.successGreen,
                size: 16,
              ),
              const SizedBox(width: AppTheme.sm),
              Expanded(
                child: Text(
                  benefit,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.gray700,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Future<void> _requestLocationPermission() async {
    setState(() {
      _isRequestingPermission = true;
    });

    try {
      final locationService = LocationService.instance;
      final granted = await locationService.requestLocationPermission();
      
      setState(() {
        _permissionGranted = granted;
        _permissionStatus = locationService.getPermissionStatusText();
        _isRequestingPermission = false;
      });
      
      widget.onLocationPermissionChanged?.call(_permissionGranted);
      
      if (!granted) {
        _showPermissionDeniedDialog();
      }
    } catch (e) {
      setState(() {
        _isRequestingPermission = false;
      });
      
      _showErrorDialog('Failed to request location permission: $e');
    }
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Location Permission',
          style: AppTheme.h4.copyWith(color: AppTheme.gray900),
        ),
        content: Text(
          'Location permission is required to show nearby businesses. You can enable it later in Settings.',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final locationService = LocationService.instance;
              await locationService.openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Error',
          style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
        ),
        content: Text(
          message,
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
