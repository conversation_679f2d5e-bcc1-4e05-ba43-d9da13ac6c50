import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/location_service.dart';
import '../widgets/onboarding_page.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLocationPermissionGranted = false;

  final List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: 'Discover Local Businesses',
      description: 'Find the best restaurants, shops, and services in your area with personalized recommendations.',
      imagePath: 'assets/images/onboarding_1.png',
      icon: FeatherIcons.search,
    ),
    OnboardingPageData(
      title: 'Read Authentic Reviews',
      description: 'Make informed decisions with genuine reviews and ratings from real customers.',
      imagePath: 'assets/images/onboarding_2.png',
      icon: FeatherIcons.star,
    ),
    OnboardingPageData(
      title: 'Navigate with Ease',
      description: 'Get directions, contact information, and business hours all in one place.',
      imagePath: 'assets/images/onboarding_3.png',
      icon: FeatherIcons.navigation,
    ),
    OnboardingPageData(
      title: 'Enable Location Services',
      description: 'Allow location access to find businesses near you and get personalized recommendations.',
      imagePath: 'assets/images/onboarding_4.png',
      icon: FeatherIcons.mapPin,
      isLocationPage: true,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.white,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            if (_currentPage < _pages.length - 1)
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.all(AppTheme.lg),
                  child: TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'Skip',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.gray500,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              )
            else
              const SizedBox(height: 60), // Maintain spacing

            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return OnboardingPage(
                    data: _pages[index],
                    onLocationPermissionChanged: (granted) {
                      setState(() {
                        _isLocationPermissionGranted = granted;
                      });
                    },
                  );
                },
              ),
            ),

            // Page indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_pages.length, (index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentPage == index ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? AppTheme.primaryPurple
                        : AppTheme.gray300,
                    borderRadius: BorderRadius.circular(4),
                  ),
                );
              }),
            ),

            const SizedBox(height: AppTheme.xxl),

            // Navigation buttons
            Padding(
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Row(
                children: [
                  // Back button
                  if (_currentPage > 0)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _previousPage,
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppTheme.gray300),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppTheme.md),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: AppTheme.lg),
                        ),
                        child: Text(
                          'Back',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.gray700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                  else
                    const Expanded(child: SizedBox()),

                  if (_currentPage > 0) const SizedBox(width: AppTheme.lg),

                  // Next/Get Started button
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLastPage() ? _finishOnboarding : _nextPage,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: AppTheme.lg),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppTheme.md),
                        ),
                      ),
                      child: Text(
                        _getButtonText(),
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isLastPage() => _currentPage == _pages.length - 1;

  String _getButtonText() {
    if (_isLastPage()) {
      return _isLocationPermissionGranted ? 'Get Started' : 'Continue Without Location';
    }
    return 'Next';
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _skipOnboarding() async {
    await _completeOnboarding();
  }

  Future<void> _finishOnboarding() async {
    await _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    try {
      // Mark onboarding as completed
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed', true);

      if (mounted) {
        // Navigate to main app
        context.go('/home');
      }
    } catch (e) {
      // Handle error gracefully
      if (mounted) {
        context.go('/home');
      }
    }
  }
}

class OnboardingPageData {
  final String title;
  final String description;
  final String imagePath;
  final IconData icon;
  final bool isLocationPage;

  const OnboardingPageData({
    required this.title,
    required this.description,
    required this.imagePath,
    required this.icon,
    this.isLocationPage = false,
  });
}
