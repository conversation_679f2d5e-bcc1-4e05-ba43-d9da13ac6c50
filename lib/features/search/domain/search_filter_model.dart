import 'package:flutter/foundation.dart';

enum SortOption {
  relevance,
  distance,
  rating,
  priceAsc,
  priceDesc,
  newest,
}

enum ViewMode {
  list,
  grid,
}

class SearchFilterModel {
  final String? query;
  final List<int> categoryIds;
  final double? latitude;
  final double? longitude;
  final int? radiusKm;
  final double? minRating;
  final List<String> priceRanges; // ['$', '$$', '$$$', '$$$$']
  final bool? openNow;
  final bool? hasProducts;
  final bool? hasServices;
  final SortOption sortBy;
  final ViewMode viewMode;

  const SearchFilterModel({
    this.query,
    this.categoryIds = const [],
    this.latitude,
    this.longitude,
    this.radiusKm,
    this.minRating,
    this.priceRanges = const [],
    this.openNow,
    this.hasProducts,
    this.hasServices,
    this.sortBy = SortOption.relevance,
    this.viewMode = ViewMode.list,
  });

  SearchFilterModel copyWith({
    String? query,
    List<int>? categoryIds,
    double? latitude,
    double? longitude,
    int? radiusKm,
    double? minRating,
    List<String>? priceRanges,
    bool? openNow,
    bool? hasProducts,
    bool? hasServices,
    SortOption? sortBy,
    ViewMode? viewMode,
  }) {
    return SearchFilterModel(
      query: query ?? this.query,
      categoryIds: categoryIds ?? this.categoryIds,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      radiusKm: radiusKm ?? this.radiusKm,
      minRating: minRating ?? this.minRating,
      priceRanges: priceRanges ?? this.priceRanges,
      openNow: openNow ?? this.openNow,
      hasProducts: hasProducts ?? this.hasProducts,
      hasServices: hasServices ?? this.hasServices,
      sortBy: sortBy ?? this.sortBy,
      viewMode: viewMode ?? this.viewMode,
    );
  }

  /// Get the number of active filters (excluding query, sort, and view mode)
  int get activeFilterCount {
    int count = 0;
    if (categoryIds.isNotEmpty) count++;
    if (radiusKm != null) count++;
    if (minRating != null) count++;
    if (priceRanges.isNotEmpty) count++;
    if (openNow == true) count++;
    if (hasProducts == true) count++;
    if (hasServices == true) count++;
    return count;
  }

  /// Check if any filters are active
  bool get hasActiveFilters => activeFilterCount > 0;

  /// Clear all filters except query
  SearchFilterModel clearFilters() {
    return SearchFilterModel(
      query: query,
      sortBy: sortBy,
      viewMode: viewMode,
    );
  }

  /// Convert to API query parameters
  Map<String, dynamic> toApiParams() {
    final params = <String, dynamic>{};
    
    if (query != null && query!.isNotEmpty) {
      params['search'] = query;
    }
    
    if (categoryIds.isNotEmpty) {
      params['category'] = categoryIds.join(',');
    }
    
    if (latitude != null && longitude != null) {
      params['lat'] = latitude;
      params['lng'] = longitude;
    }
    
    if (radiusKm != null) {
      params['radius'] = radiusKm;
    }
    
    if (minRating != null) {
      params['rating'] = minRating;
    }
    
    if (priceRanges.isNotEmpty) {
      params['price_range'] = priceRanges.join(',');
    }
    
    if (openNow != null) {
      params['open_now'] = openNow;
    }
    
    if (hasProducts != null) {
      params['has_products'] = hasProducts;
    }
    
    if (hasServices != null) {
      params['has_services'] = hasServices;
    }
    
    // Add sort parameter
    switch (sortBy) {
      case SortOption.distance:
        params['sort'] = 'distance';
        break;
      case SortOption.rating:
        params['sort'] = 'rating';
        break;
      case SortOption.priceAsc:
        params['sort'] = 'price_asc';
        break;
      case SortOption.priceDesc:
        params['sort'] = 'price_desc';
        break;
      case SortOption.newest:
        params['sort'] = 'newest';
        break;
      case SortOption.relevance:
      default:
        params['sort'] = 'relevance';
        break;
    }
    
    return params;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchFilterModel &&
        other.query == query &&
        listEquals(other.categoryIds, categoryIds) &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.radiusKm == radiusKm &&
        other.minRating == minRating &&
        listEquals(other.priceRanges, priceRanges) &&
        other.openNow == openNow &&
        other.hasProducts == hasProducts &&
        other.hasServices == hasServices &&
        other.sortBy == sortBy &&
        other.viewMode == viewMode;
  }

  @override
  int get hashCode {
    return Object.hash(
      query,
      Object.hashAll(categoryIds),
      latitude,
      longitude,
      radiusKm,
      minRating,
      Object.hashAll(priceRanges),
      openNow,
      hasProducts,
      hasServices,
      sortBy,
      viewMode,
    );
  }

  @override
  String toString() {
    return 'SearchFilterModel(query: $query, activeFilters: $activeFilterCount, sortBy: $sortBy, viewMode: $viewMode)';
  }
}

extension SortOptionExtension on SortOption {
  String get displayName {
    switch (this) {
      case SortOption.relevance:
        return 'Relevance';
      case SortOption.distance:
        return 'Distance';
      case SortOption.rating:
        return 'Rating';
      case SortOption.priceAsc:
        return 'Price: Low to High';
      case SortOption.priceDesc:
        return 'Price: High to Low';
      case SortOption.newest:
        return 'Newest';
    }
  }
}

extension ViewModeExtension on ViewMode {
  String get displayName {
    switch (this) {
      case ViewMode.list:
        return 'List';
      case ViewMode.grid:
        return 'Grid';
    }
  }
}
