import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../business/data/business_repository.dart';
import '../../business/domain/business_summary_model.dart';
import '../domain/search_filter_model.dart';

class SearchNotifier extends ChangeNotifier {
  final BusinessRepository _businessRepository;

  bool _isLoading = false;
  List<BusinessSummaryModel> _results = [];
  String? _errorMessage;
  bool _initialSearch = true;
  Timer? _debounceTimer;
  SearchFilterModel _filters = const SearchFilterModel();
  List<String> _recentSearches = [];
  List<String> _searchSuggestions = [];

  SearchNotifier(this._businessRepository) {
    _loadRecentSearches();
  }

  // Getters
  bool get isLoading => _isLoading;
  List<BusinessSummaryModel> get results => _results;
  String? get errorMessage => _errorMessage;
  bool get initialSearch => _initialSearch;
  SearchFilterModel get filters => _filters;
  List<String> get recentSearches => _recentSearches;
  List<String> get searchSuggestions => _searchSuggestions;
  String get currentQuery => _filters.query ?? '';
  int get activeFilterCount => _filters.activeFilterCount;

  // Load recent searches from storage
  Future<void> _loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _recentSearches = prefs.getStringList('recent_searches') ?? [];
      notifyListeners();
    } catch (e) {
      // Handle error silently
    }
  }

  // Save recent searches to storage
  Future<void> _saveRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('recent_searches', _recentSearches);
    } catch (e) {
      // Handle error silently
    }
  }

  // Add to recent searches
  void _addToRecentSearches(String query) {
    if (query.trim().isEmpty) return;

    _recentSearches.remove(query); // Remove if exists
    _recentSearches.insert(0, query); // Add to beginning

    // Keep only last 10 searches
    if (_recentSearches.length > 10) {
      _recentSearches = _recentSearches.take(10).toList();
    }

    _saveRecentSearches();
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear results and reset to initial state
  void clearResults() {
    _results = [];
    _initialSearch = true;
    _filters = const SearchFilterModel();
    _errorMessage = null;
    notifyListeners();
  }

  // Update filters
  void updateFilters(SearchFilterModel newFilters) {
    _filters = newFilters;
    notifyListeners();

    // Perform search if there's a query
    if (newFilters.query != null && newFilters.query!.isNotEmpty) {
      _performSearch();
    }
  }

  // Search with debounce
  Future<void> search(String query) async {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Update filters with new query
    _filters = _filters.copyWith(query: query);

    // If query is empty, clear results
    if (query.trim().isEmpty) {
      _results = [];
      _initialSearch = true;
      _errorMessage = null;
      notifyListeners();
      return;
    }

    // Set up debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch();
    });
  }

  // Perform the actual search
  Future<void> _performSearch() async {
    final query = _filters.query;
    if (query == null || query.isEmpty) return;

    _isLoading = true;
    _errorMessage = null;
    _initialSearch = false;
    notifyListeners();

    try {
      // Add to recent searches
      _addToRecentSearches(query);

      // Convert filters to API parameters
      final apiParams = _filters.toApiParams();

      final results = await _businessRepository.searchBusinesses(apiParams);

      _results = results;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search by category
  Future<void> searchByCategory(int categoryId) async {
    _filters = _filters.copyWith(categoryIds: [categoryId]);
    await _performSearch();
  }

  // Quick search methods
  Future<void> searchNearby({double? latitude, double? longitude}) async {
    _filters = _filters.copyWith(
      latitude: latitude,
      longitude: longitude,
      radiusKm: 10,
    );
    await _performSearch();
  }

  Future<void> searchOpenNow() async {
    _filters = _filters.copyWith(openNow: true);
    await _performSearch();
  }

  // Sort and view mode methods
  void setSortOption(SortOption sortOption) {
    _filters = _filters.copyWith(sortBy: sortOption);
    notifyListeners();
    if (!_initialSearch && _results.isNotEmpty) {
      _performSearch();
    }
  }

  void setViewMode(ViewMode viewMode) {
    _filters = _filters.copyWith(viewMode: viewMode);
    notifyListeners();
  }

  // Filter management
  void clearFilters() {
    final query = _filters.query;
    _filters = SearchFilterModel(query: query);
    notifyListeners();
    if (query != null && query.isNotEmpty) {
      _performSearch();
    }
  }

  void removeFilter(String filterType) {
    switch (filterType) {
      case 'categories':
        _filters = _filters.copyWith(categoryIds: []);
        break;
      case 'radius':
        _filters = _filters.copyWith(radiusKm: null);
        break;
      case 'rating':
        _filters = _filters.copyWith(minRating: null);
        break;
      case 'price':
        _filters = _filters.copyWith(priceRanges: []);
        break;
      case 'open_now':
        _filters = _filters.copyWith(openNow: null);
        break;
      case 'has_products':
        _filters = _filters.copyWith(hasProducts: null);
        break;
      case 'has_services':
        _filters = _filters.copyWith(hasServices: null);
        break;
    }
    notifyListeners();
    if (_filters.query != null && _filters.query!.isNotEmpty) {
      _performSearch();
    }
  }

  // Clear recent searches
  void clearRecentSearches() {
    _recentSearches.clear();
    _saveRecentSearches();
    notifyListeners();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}
