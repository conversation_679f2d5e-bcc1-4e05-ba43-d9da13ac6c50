import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/search_filter_model.dart';
import '../providers/search_notifier.dart';
import '../../../core/theme/app_theme.dart';

class SortBottomSheet extends StatelessWidget {
  final SortOption currentSort;

  const SortBottomSheet({
    super.key,
    required this.currentSort,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Sort by',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(FeatherIcons.x, color: AppTheme.gray500),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Sort options
          ...SortOption.values.map((option) => _buildSortOption(
            context,
            option,
            currentSort == option,
          )),
          
          const SizedBox(height: AppTheme.lg),
        ],
      ),
    );
  }

  Widget _buildSortOption(BuildContext context, SortOption option, bool isSelected) {
    IconData icon;
    switch (option) {
      case SortOption.relevance:
        icon = FeatherIcons.search;
        break;
      case SortOption.distance:
        icon = FeatherIcons.navigation;
        break;
      case SortOption.rating:
        icon = FeatherIcons.star;
        break;
      case SortOption.priceAsc:
      case SortOption.priceDesc:
        icon = FeatherIcons.dollarSign;
        break;
      case SortOption.newest:
        icon = FeatherIcons.clock;
        break;
    }

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryPurple : AppTheme.gray500,
      ),
      title: Text(
        option.displayName,
        style: AppTheme.bodyMedium.copyWith(
          color: isSelected ? AppTheme.primaryPurple : AppTheme.gray900,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              FeatherIcons.check,
              color: AppTheme.primaryPurple,
            )
          : null,
      onTap: () {
        context.read<SearchNotifier>().setSortOption(option);
        Navigator.of(context).pop();
      },
    );
  }
}
