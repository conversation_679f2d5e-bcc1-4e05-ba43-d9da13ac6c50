import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/search_filter_model.dart';
import '../providers/search_notifier.dart';
import '../../discovery/domain/category_model.dart';
import '../../business/data/business_repository.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/filter_chips.dart' as custom_chips;

class FilterBottomSheet extends StatefulWidget {
  final SearchFilterModel initialFilters;

  const FilterBottomSheet({super.key, required this.initialFilters});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late SearchFilterModel _filters;
  List<CategoryModel> _categories = [];
  bool _categoriesLoading = true;

  @override
  void initState() {
    super.initState();
    _filters = widget.initialFilters;
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await context
          .read<BusinessRepository>()
          .getCategories();
      setState(() {
        _categories = categories;
        _categoriesLoading = false;
      });
    } catch (e) {
      setState(() {
        _categoriesLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Filters',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearAllFilters,
                  child: Text(
                    'Clear All',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryPurple,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCategoriesSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildDistanceSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildRatingSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildPriceSection(),
                  const SizedBox(height: AppTheme.xxl),
                  _buildQuickFiltersSection(),
                ],
              ),
            ),
          ),

          // Apply button
          Container(
            padding: const EdgeInsets.all(AppTheme.lg),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: AppTheme.gray300)),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                child: Text('Apply Filters (${_filters.activeFilterCount})'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        if (_categoriesLoading)
          const Center(child: CircularProgressIndicator())
        else
          Wrap(
            spacing: AppTheme.sm,
            runSpacing: AppTheme.sm,
            children: _categories.map((category) {
              final isSelected = _filters.categoryIds.contains(category.id);
              return custom_chips.FilterChip(
                label: category.name,
                isSelected: isSelected,
                onTap: () => _toggleCategory(category.id),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildDistanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Distance', style: AppTheme.h5.copyWith(color: AppTheme.gray900)),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [1, 5, 10, 25].map((km) {
            final isSelected = _filters.radiusKm == km;
            return custom_chips.FilterChip(
              label: 'Within ${km}km',
              isSelected: isSelected,
              icon: FeatherIcons.mapPin,
              selectedColor: AppTheme.distanceBlue,
              onTap: () => _setDistance(km),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Minimum Rating',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [3.0, 3.5, 4.0, 4.5].map((rating) {
            final isSelected = _filters.minRating == rating;
            return custom_chips.FilterChip(
              label: '$rating+ Stars',
              isSelected: isSelected,
              icon: FeatherIcons.star,
              selectedColor: AppTheme.goldStar,
              onTap: () => _setRating(rating),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Price Range',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: ['\$', '\$\$', '\$\$\$', '\$\$\$\$'].map((price) {
            final isSelected = _filters.priceRanges.contains(price);
            return custom_chips.FilterChip(
              label: price,
              isSelected: isSelected,
              icon: FeatherIcons.dollarSign,
              onTap: () => _togglePriceRange(price),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Filters',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [
            custom_chips.FilterChip(
              label: 'Open Now',
              isSelected: _filters.openNow == true,
              icon: FeatherIcons.clock,
              selectedColor: AppTheme.successGreen,
              onTap: () => _toggleOpenNow(),
            ),
            custom_chips.FilterChip(
              label: 'Has Products',
              isSelected: _filters.hasProducts == true,
              icon: FeatherIcons.package,
              onTap: () => _toggleHasProducts(),
            ),
            custom_chips.FilterChip(
              label: 'Has Services',
              isSelected: _filters.hasServices == true,
              icon: FeatherIcons.settings,
              onTap: () => _toggleHasServices(),
            ),
          ],
        ),
      ],
    );
  }

  void _toggleCategory(int categoryId) {
    final categories = List<int>.from(_filters.categoryIds);
    if (categories.contains(categoryId)) {
      categories.remove(categoryId);
    } else {
      categories.add(categoryId);
    }
    setState(() {
      _filters = _filters.copyWith(categoryIds: categories);
    });
  }

  void _setDistance(int km) {
    setState(() {
      _filters = _filters.copyWith(
        radiusKm: _filters.radiusKm == km ? null : km,
      );
    });
  }

  void _setRating(double rating) {
    setState(() {
      _filters = _filters.copyWith(
        minRating: _filters.minRating == rating ? null : rating,
      );
    });
  }

  void _togglePriceRange(String price) {
    final priceRanges = List<String>.from(_filters.priceRanges);
    if (priceRanges.contains(price)) {
      priceRanges.remove(price);
    } else {
      priceRanges.add(price);
    }
    setState(() {
      _filters = _filters.copyWith(priceRanges: priceRanges);
    });
  }

  void _toggleOpenNow() {
    setState(() {
      _filters = _filters.copyWith(
        openNow: _filters.openNow == true ? null : true,
      );
    });
  }

  void _toggleHasProducts() {
    setState(() {
      _filters = _filters.copyWith(
        hasProducts: _filters.hasProducts == true ? null : true,
      );
    });
  }

  void _toggleHasServices() {
    setState(() {
      _filters = _filters.copyWith(
        hasServices: _filters.hasServices == true ? null : true,
      );
    });
  }

  void _clearAllFilters() {
    setState(() {
      _filters = _filters.clearFilters();
    });
  }

  void _applyFilters() {
    context.read<SearchNotifier>().updateFilters(_filters);
    Navigator.of(context).pop();
  }
}
