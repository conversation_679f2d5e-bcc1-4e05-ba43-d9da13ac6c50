import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../providers/search_notifier.dart';
import '../../discovery/domain/category_model.dart';
import '../../business/data/business_repository.dart';
import '../../../core/theme/app_theme.dart';

class SearchSuggestions extends StatefulWidget {
  final String query;
  final Function(String) onSuggestionTap;

  const SearchSuggestions({
    super.key,
    required this.query,
    required this.onSuggestionTap,
  });

  @override
  State<SearchSuggestions> createState() => _SearchSuggestionsState();
}

class _SearchSuggestionsState extends State<SearchSuggestions> {
  List<CategoryModel> _categories = [];
  List<String> _popularSearches = [
    'Restaurants',
    'Coffee shops',
    'Gas stations',
    'Pharmacies',
    'Grocery stores',
    'Banks',
    'Hair salons',
    'Auto repair',
  ];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await context.read<BusinessRepository>().getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    final searchNotifier = context.watch<SearchNotifier>();
    
    return Container(
      color: AppTheme.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent searches
          if (searchNotifier.recentSearches.isNotEmpty && widget.query.isEmpty) ...[
            _buildSectionHeader(
              'Recent Searches',
              onClear: () => searchNotifier.clearRecentSearches(),
            ),
            ...searchNotifier.recentSearches.map((search) => _buildSearchItem(
              search,
              FeatherIcons.clock,
              onTap: () => widget.onSuggestionTap(search),
            )),
            const SizedBox(height: AppTheme.lg),
          ],

          // Category suggestions based on query
          if (widget.query.isNotEmpty) ...[
            ..._getMatchingCategories(widget.query).map((category) => _buildSearchItem(
              category.name,
              FeatherIcons.tag,
              onTap: () => widget.onSuggestionTap(category.name),
            )),
            if (_getMatchingCategories(widget.query).isNotEmpty)
              const SizedBox(height: AppTheme.lg),
          ],

          // Popular searches
          if (widget.query.isEmpty) ...[
            _buildSectionHeader('Popular Searches'),
            ..._popularSearches.map((search) => _buildSearchItem(
              search,
              FeatherIcons.trendingUp,
              onTap: () => widget.onSuggestionTap(search),
            )),
            const SizedBox(height: AppTheme.lg),
          ],

          // Quick search suggestions based on query
          if (widget.query.isNotEmpty) ...[
            ..._getQuerySuggestions(widget.query).map((suggestion) => _buildSearchItem(
              suggestion,
              FeatherIcons.search,
              onTap: () => widget.onSuggestionTap(suggestion),
            )),
          ],

          // Browse categories
          if (widget.query.isEmpty && _categories.isNotEmpty) ...[
            _buildSectionHeader('Browse Categories'),
            ..._categories.take(8).map((category) => _buildSearchItem(
              category.name,
              FeatherIcons.grid,
              onTap: () => widget.onSuggestionTap(category.name),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, {VoidCallback? onClear}) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.md,
      ),
      child: Row(
        children: [
          Text(
            title,
            style: AppTheme.h5.copyWith(color: AppTheme.gray700),
          ),
          const Spacer(),
          if (onClear != null)
            TextButton(
              onPressed: onClear,
              child: Text(
                'Clear',
                style: AppTheme.bodySmall.copyWith(color: AppTheme.primaryPurple),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchItem(
    String text,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppTheme.gray500,
        size: 20,
      ),
      title: Text(
        text,
        style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
      ),
      trailing: const Icon(
        FeatherIcons.arrowUpLeft,
        color: AppTheme.gray400,
        size: 16,
      ),
      onTap: onTap,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
    );
  }

  List<CategoryModel> _getMatchingCategories(String query) {
    if (query.isEmpty) return [];
    
    return _categories
        .where((category) =>
            category.name.toLowerCase().contains(query.toLowerCase()))
        .take(3)
        .toList();
  }

  List<String> _getQuerySuggestions(String query) {
    if (query.isEmpty) return [];
    
    final suggestions = <String>[];
    
    // Add "near me" suggestion
    suggestions.add('$query near me');
    
    // Add specific suggestions based on query
    if (query.toLowerCase().contains('food') || 
        query.toLowerCase().contains('restaurant')) {
      suggestions.addAll([
        '$query delivery',
        '$query open now',
        '$query with parking',
      ]);
    } else if (query.toLowerCase().contains('shop') || 
               query.toLowerCase().contains('store')) {
      suggestions.addAll([
        '$query open late',
        '$query with parking',
        '$query reviews',
      ]);
    } else {
      suggestions.addAll([
        '$query open now',
        '$query reviews',
        '$query hours',
      ]);
    }
    
    return suggestions.take(4).toList();
  }
}

class SearchInitialState extends StatelessWidget {
  const SearchInitialState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FeatherIcons.search,
              size: 64,
              color: AppTheme.gray400,
            ),
            const SizedBox(height: AppTheme.lg),
            Text(
              'Search for local businesses',
              style: AppTheme.h4.copyWith(color: AppTheme.gray700),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              'Try searching for restaurants, shops, services, and more',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
