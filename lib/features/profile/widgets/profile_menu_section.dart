import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';
import '../screens/enhanced_profile_screen.dart';

class ProfileMenuSection extends StatelessWidget {
  final String title;
  final List<ProfileMenuItem> items;

  const ProfileMenuSection({
    super.key,
    required this.title,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.fromLTRB(
              AppTheme.lg,
              AppTheme.lg,
              AppTheme.lg,
              AppTheme.sm,
            ),
            child: Text(
              title,
              style: AppTheme.h5.copyWith(
                color: AppTheme.gray900,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Menu items
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == items.length - 1;
            
            return Column(
              children: [
                _buildMenuItem(item),
                if (!isLast)
                  const Divider(
                    height: 1,
                    indent: AppTheme.lg,
                    endIndent: AppTheme.lg,
                  ),
              ],
            );
          }),
          
          const SizedBox(height: AppTheme.sm),
        ],
      ),
    );
  }

  Widget _buildMenuItem(ProfileMenuItem item) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.primaryPurple.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.sm),
        ),
        child: Icon(
          item.icon,
          color: AppTheme.primaryPurple,
          size: 20,
        ),
      ),
      title: Text(
        item.title,
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.gray900,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        item.subtitle,
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.gray600,
        ),
      ),
      trailing: const Icon(
        FeatherIcons.chevronRight,
        color: AppTheme.gray400,
        size: 16,
      ),
      onTap: item.onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
    );
  }
}
