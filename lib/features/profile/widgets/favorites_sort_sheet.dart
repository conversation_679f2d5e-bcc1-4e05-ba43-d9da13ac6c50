import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';

class FavoritesSortSheet extends StatelessWidget {
  final String currentSort;
  final Function(String) onSortChanged;

  const FavoritesSortSheet({
    super.key,
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Sort Favorites',
                  style: AppTheme.h4.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(FeatherIcons.x, color: AppTheme.gray500),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Sort options
          _buildSortOption(
            context,
            'recent',
            'Recently Added',
            'Most recently favorited first',
            FeatherIcons.clock,
          ),
          
          _buildSortOption(
            context,
            'name',
            'Name',
            'Alphabetical order',
            FeatherIcons.type,
          ),
          
          _buildSortOption(
            context,
            'rating',
            'Rating',
            'Highest rated first',
            FeatherIcons.star,
          ),
          
          _buildSortOption(
            context,
            'distance',
            'Distance',
            'Closest to you first',
            FeatherIcons.navigation,
          ),
          
          const SizedBox(height: AppTheme.lg),
        ],
      ),
    );
  }

  Widget _buildSortOption(
    BuildContext context,
    String value,
    String title,
    String description,
    IconData icon,
  ) {
    final isSelected = currentSort == value;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryPurple : AppTheme.gray600,
        size: 20,
      ),
      title: Text(
        title,
        style: AppTheme.bodyMedium.copyWith(
          color: isSelected ? AppTheme.primaryPurple : AppTheme.gray900,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      subtitle: Text(
        description,
        style: AppTheme.bodySmall.copyWith(
          color: isSelected ? AppTheme.primaryPurple.withOpacity(0.7) : AppTheme.gray500,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              FeatherIcons.check,
              color: AppTheme.primaryPurple,
              size: 20,
            )
          : null,
      onTap: () {
        onSortChanged(value);
        Navigator.of(context).pop();
      },
    );
  }
}
