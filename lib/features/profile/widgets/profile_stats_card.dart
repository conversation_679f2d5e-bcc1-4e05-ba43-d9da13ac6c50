import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';

class ProfileStatsCard extends StatelessWidget {
  final int favoriteCount;
  final int reviewCount;
  final bool isLoading;

  const ProfileStatsCard({
    super.key,
    required this.favoriteCount,
    required this.reviewCount,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
      padding: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Activity',
            style: AppTheme.h5.copyWith(
              color: AppTheme.gray900,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppTheme.lg),
          
          if (isLoading) ...[
            _buildLoadingStats(),
          ] else ...[
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: FeatherIcons.heart,
                    label: 'Favorites',
                    value: favoriteCount.toString(),
                    color: AppTheme.heartRed,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppTheme.gray300,
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: FeatherIcons.star,
                    label: 'Reviews',
                    value: reviewCount.toString(),
                    color: AppTheme.goldStar,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppTheme.gray300,
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: FeatherIcons.mapPin,
                    label: 'Visited',
                    value: '0', // TODO: Implement visited count
                    color: AppTheme.distanceBlue,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: AppTheme.sm),
        Text(
          value,
          style: AppTheme.h4.copyWith(
            color: AppTheme.gray900,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.xs),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.gray600,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingStats() {
    return Row(
      children: [
        Expanded(child: _buildLoadingStat()),
        Container(
          width: 1,
          height: 40,
          color: AppTheme.gray300,
        ),
        Expanded(child: _buildLoadingStat()),
        Container(
          width: 1,
          height: 40,
          color: AppTheme.gray300,
        ),
        Expanded(child: _buildLoadingStat()),
      ],
    );
  }

  Widget _buildLoadingStat() {
    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.gray300,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(height: AppTheme.sm),
        Container(
          width: 24,
          height: 20,
          decoration: BoxDecoration(
            color: AppTheme.gray300,
            borderRadius: BorderRadius.circular(AppTheme.xs),
          ),
        ),
        const SizedBox(height: AppTheme.xs),
        Container(
          width: 40,
          height: 12,
          decoration: BoxDecoration(
            color: AppTheme.gray300,
            borderRadius: BorderRadius.circular(AppTheme.xs),
          ),
        ),
      ],
    );
  }
}
