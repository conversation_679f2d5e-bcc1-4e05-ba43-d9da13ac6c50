import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/app_initialization_service.dart';

class ProfileSettingsSheet extends StatefulWidget {
  const ProfileSettingsSheet({super.key});

  @override
  State<ProfileSettingsSheet> createState() => _ProfileSettingsSheetState();
}

class _ProfileSettingsSheetState extends State<ProfileSettingsSheet> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _marketingEmails = false;
  String _distanceUnit = 'km';
  String _theme = 'system';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final appInit = AppInitializationService.instance;
    
    final notifications = await appInit.getUserPreference<bool>('notifications_enabled') ?? true;
    final location = await appInit.getUserPreference<bool>('location_enabled') ?? true;
    final marketing = await appInit.getUserPreference<bool>('marketing_emails') ?? false;
    final distance = await appInit.getUserPreference<String>('distance_unit') ?? 'km';
    final themeMode = await appInit.getUserPreference<String>('theme_mode') ?? 'system';
    
    setState(() {
      _notificationsEnabled = notifications;
      _locationEnabled = location;
      _marketingEmails = marketing;
      _distanceUnit = distance;
      _theme = themeMode;
    });
  }

  Future<void> _saveSettings() async {
    final appInit = AppInitializationService.instance;
    
    await appInit.saveUserPreference('notifications_enabled', _notificationsEnabled);
    await appInit.saveUserPreference('location_enabled', _locationEnabled);
    await appInit.saveUserPreference('marketing_emails', _marketingEmails);
    await appInit.saveUserPreference('distance_unit', _distanceUnit);
    await appInit.saveUserPreference('theme_mode', _theme);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Settings',
                  style: AppTheme.h3.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(FeatherIcons.x, color: AppTheme.gray500),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Settings content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSettingsSection(
                    'Notifications',
                    [
                      _buildSwitchTile(
                        icon: FeatherIcons.bell,
                        title: 'Push Notifications',
                        subtitle: 'Receive notifications about nearby businesses',
                        value: _notificationsEnabled,
                        onChanged: (value) {
                          setState(() {
                            _notificationsEnabled = value;
                          });
                          _saveSettings();
                        },
                      ),
                      _buildSwitchTile(
                        icon: FeatherIcons.mail,
                        title: 'Marketing Emails',
                        subtitle: 'Receive promotional emails and updates',
                        value: _marketingEmails,
                        onChanged: (value) {
                          setState(() {
                            _marketingEmails = value;
                          });
                          _saveSettings();
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppTheme.xxl),
                  
                  _buildSettingsSection(
                    'Location',
                    [
                      _buildSwitchTile(
                        icon: FeatherIcons.mapPin,
                        title: 'Location Services',
                        subtitle: 'Allow app to access your location',
                        value: _locationEnabled,
                        onChanged: (value) {
                          setState(() {
                            _locationEnabled = value;
                          });
                          _saveSettings();
                        },
                      ),
                      _buildDropdownTile(
                        icon: FeatherIcons.navigation,
                        title: 'Distance Unit',
                        subtitle: 'Choose your preferred distance unit',
                        value: _distanceUnit,
                        options: const [
                          {'value': 'km', 'label': 'Kilometers (km)'},
                          {'value': 'mi', 'label': 'Miles (mi)'},
                        ],
                        onChanged: (value) {
                          setState(() {
                            _distanceUnit = value;
                          });
                          _saveSettings();
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppTheme.xxl),
                  
                  _buildSettingsSection(
                    'Appearance',
                    [
                      _buildDropdownTile(
                        icon: FeatherIcons.moon,
                        title: 'Theme',
                        subtitle: 'Choose your preferred theme',
                        value: _theme,
                        options: const [
                          {'value': 'system', 'label': 'System Default'},
                          {'value': 'light', 'label': 'Light Mode'},
                          {'value': 'dark', 'label': 'Dark Mode'},
                        ],
                        onChanged: (value) {
                          setState(() {
                            _theme = value;
                          });
                          _saveSettings();
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppTheme.xxl),
                  
                  _buildSettingsSection(
                    'Data & Privacy',
                    [
                      _buildActionTile(
                        icon: FeatherIcons.download,
                        title: 'Export Data',
                        subtitle: 'Download your personal data',
                        onTap: () {
                          // TODO: Implement data export
                        },
                      ),
                      _buildActionTile(
                        icon: FeatherIcons.trash2,
                        title: 'Clear Cache',
                        subtitle: 'Clear app cache and temporary data',
                        onTap: () {
                          _showClearCacheDialog();
                        },
                      ),
                      _buildActionTile(
                        icon: FeatherIcons.alertTriangle,
                        title: 'Delete Account',
                        subtitle: 'Permanently delete your account',
                        onTap: () {
                          _showDeleteAccountDialog();
                        },
                        isDestructive: true,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTheme.h5.copyWith(
            color: AppTheme.gray900,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.md),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.gray100,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.gray700, size: 20),
      title: Text(
        title,
        style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.gray600),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryPurple,
      ),
    );
  }

  Widget _buildDropdownTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required String value,
    required List<Map<String, String>> options,
    required Function(String) onChanged,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.gray700, size: 20),
      title: Text(
        title,
        style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.gray600),
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: (newValue) {
          if (newValue != null) {
            onChanged(newValue);
          }
        },
        items: options.map((option) {
          return DropdownMenuItem<String>(
            value: option['value'],
            child: Text(option['label']!),
          );
        }).toList(),
        underline: const SizedBox(),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppTheme.errorRed : AppTheme.gray700,
        size: 20,
      ),
      title: Text(
        title,
        style: AppTheme.bodyMedium.copyWith(
          color: isDestructive ? AppTheme.errorRed : AppTheme.gray900,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.gray600),
      ),
      trailing: Icon(
        FeatherIcons.chevronRight,
        color: AppTheme.gray400,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Clear Cache',
          style: AppTheme.h4.copyWith(color: AppTheme.gray900),
        ),
        content: Text(
          'This will clear all cached data and temporary files. You may need to re-download some content.',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement cache clearing
            },
            child: const Text('Clear Cache'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Account',
          style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
        ),
        content: Text(
          'This action cannot be undone. All your data will be permanently deleted.',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement account deletion
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }
}
