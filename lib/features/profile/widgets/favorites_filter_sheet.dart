import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/filter_chips.dart' as custom_chips;

class FavoritesFilterSheet extends StatefulWidget {
  final List<String> availableCategories;
  final Set<String> selectedCategories;
  final double? minRating;
  final Function(Set<String>, double?) onFiltersChanged;

  const FavoritesFilterSheet({
    super.key,
    required this.availableCategories,
    required this.selectedCategories,
    required this.minRating,
    required this.onFiltersChanged,
  });

  @override
  State<FavoritesFilterSheet> createState() => _FavoritesFilterSheetState();
}

class _FavoritesFilterSheetState extends State<FavoritesFilterSheet> {
  late Set<String> _selectedCategories;
  late double? _minRating;

  @override
  void initState() {
    super.initState();
    _selectedCategories = Set.from(widget.selectedCategories);
    _minRating = widget.minRating;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
            child: Row(
              children: [
                Text(
                  'Filter Favorites',
                  style: AppTheme.h4.copyWith(color: AppTheme.gray900),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryPurple,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(FeatherIcons.x, color: AppTheme.gray500),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Categories section
                  if (widget.availableCategories.isNotEmpty) ...[
                    _buildCategoriesSection(),
                    const SizedBox(height: AppTheme.xxl),
                  ],

                  // Rating section
                  _buildRatingSection(),
                ],
              ),
            ),
          ),

          // Apply button
          Container(
            padding: const EdgeInsets.all(AppTheme.lg),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: AppTheme.gray300)),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                child: const Text('Apply Filters'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),

        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: widget.availableCategories.map((category) {
            final isSelected = _selectedCategories.contains(category);
            return custom_chips.FilterChip(
              label: category,
              isSelected: isSelected,
              selectedColor: AppTheme.primaryPurple,
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedCategories.remove(category);
                  } else {
                    _selectedCategories.add(category);
                  }
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Minimum Rating',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),

        // Rating options
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: [
            _buildRatingChip(null, 'Any Rating'),
            _buildRatingChip(4.0, '4.0+ Stars'),
            _buildRatingChip(4.5, '4.5+ Stars'),
            _buildRatingChip(5.0, '5.0 Stars'),
          ],
        ),

        const SizedBox(height: AppTheme.lg),

        // Custom rating slider
        Container(
          padding: const EdgeInsets.all(AppTheme.lg),
          decoration: BoxDecoration(
            color: AppTheme.gray100,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Custom Rating',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.gray900,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _minRating != null
                        ? '${_minRating!.toStringAsFixed(1)}+'
                        : 'Any',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryPurple,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.sm),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppTheme.goldStar,
                  inactiveTrackColor: AppTheme.gray300,
                  thumbColor: AppTheme.goldStar,
                  overlayColor: AppTheme.goldStar.withOpacity(0.2),
                  trackHeight: 4,
                ),
                child: Slider(
                  value: _minRating ?? 0.0,
                  min: 0.0,
                  max: 5.0,
                  divisions: 10,
                  onChanged: (value) {
                    setState(() {
                      _minRating = value == 0.0 ? null : value;
                    });
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Any',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                  ),
                  Text(
                    '5.0',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingChip(double? rating, String label) {
    final isSelected = _minRating == rating;

    return GestureDetector(
      onTap: () {
        setState(() {
          _minRating = rating;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.md,
          vertical: AppTheme.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.goldStar : AppTheme.gray100,
          borderRadius: BorderRadius.circular(AppTheme.lg),
          border: Border.all(
            color: isSelected ? AppTheme.goldStar : AppTheme.gray300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (rating != null) ...[
              const Icon(Icons.star, size: 16, color: AppTheme.white),
              const SizedBox(width: AppTheme.xs),
            ],
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: isSelected ? AppTheme.white : AppTheme.gray700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedCategories.clear();
      _minRating = null;
    });
  }

  void _applyFilters() {
    widget.onFiltersChanged(_selectedCategories, _minRating);
    Navigator.of(context).pop();
  }
}
