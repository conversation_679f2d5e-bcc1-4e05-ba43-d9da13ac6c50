import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../providers/favorites_notifier.dart';
import '../../business/domain/business_summary_model.dart';
import '../../../shared/widgets/enhanced_business_card.dart';
import '../../../shared/widgets/enhanced_search_bar.dart';
import '../../../core/theme/app_theme.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  List<BusinessSummaryModel> _favoriteBusinesses = [];
  List<BusinessSummaryModel> _filteredBusinesses = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final favoritesNotifier = context.read<FavoritesNotifier>();
      final businesses = await favoritesNotifier.getFavoriteBusinesses();

      if (mounted) {
        setState(() {
          _favoriteBusinesses = businesses;
          _filteredBusinesses = businesses;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
          _isLoading = false;
        });
      }
    }
  }

  void _filterBusinesses(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredBusinesses = _favoriteBusinesses;
      } else {
        _filteredBusinesses = _favoriteBusinesses
            .where(
              (business) =>
                  business.name.toLowerCase().contains(query.toLowerCase()) ||
                  business.address.toLowerCase().contains(query.toLowerCase()),
            )
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      appBar: AppBar(
        title: Text(
          'My Favorites',
          style: AppTheme.h3.copyWith(color: AppTheme.gray900),
        ),
        backgroundColor: AppTheme.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Column(
        children: [
          // Search Bar
          if (_favoriteBusinesses.isNotEmpty)
            EnhancedSearchBar(
              hintText: 'Search favorites...',
              onChanged: _filterBusinesses,
              showFilterButton: false,
            ),
          // Body Content
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_favoriteBusinesses.isEmpty) {
      return _buildEmptyState();
    }

    return _buildFavoritesList();
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FeatherIcons.alertCircle,
              size: 64,
              color: AppTheme.errorRed,
            ),
            const SizedBox(height: AppTheme.lg),
            Text(
              'Oops! Something went wrong',
              style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              _errorMessage!,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.lg),
            ElevatedButton(
              onPressed: _loadFavorites,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(FeatherIcons.heart, size: 64, color: AppTheme.gray500),
            const SizedBox(height: AppTheme.lg),
            Text(
              'No favorites yet',
              style: AppTheme.h4.copyWith(color: AppTheme.gray700),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              'Start exploring and add businesses to your favorites to see them here.',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.xxl),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to search or home
                DefaultTabController.of(context).animateTo(0); // Go to home tab
              },
              icon: const Icon(FeatherIcons.search),
              label: const Text('Discover Businesses'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesList() {
    return RefreshIndicator(
      onRefresh: _loadFavorites,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: AppTheme.sm),
        itemCount: _filteredBusinesses.length,
        itemBuilder: (context, index) {
          return Consumer<FavoritesNotifier>(
            builder: (context, favoritesNotifier, child) {
              final business = _filteredBusinesses[index];

              // Remove from list if no longer favorited
              if (!favoritesNotifier.isFavorite(business.id)) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {
                    _favoriteBusinesses.removeWhere((b) => b.id == business.id);
                    _filterBusinesses(_searchQuery); // Re-filter
                  });
                });
                return const SizedBox.shrink();
              }

              return EnhancedBusinessCard(business: business);
            },
          );
        },
      ),
    );
  }
}
