import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../providers/favorites_notifier.dart';
import '../../auth/providers/auth_notifier.dart';
import '../widgets/profile_stats_card.dart';
import '../widgets/profile_menu_section.dart';
import '../widgets/profile_settings_sheet.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/app_initialization_service.dart';

class EnhancedProfileScreen extends StatefulWidget {
  const EnhancedProfileScreen({super.key});

  @override
  State<EnhancedProfileScreen> createState() => _EnhancedProfileScreenState();
}

class _EnhancedProfileScreenState extends State<EnhancedProfileScreen> {
  int _favoriteCount = 0;
  int _reviewCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserStats();
  }

  Future<void> _loadUserStats() async {
    try {
      final favoritesNotifier = context.read<FavoritesNotifier>();
      final favorites = await favoritesNotifier.getFavoriteBusinesses();
      
      setState(() {
        _favoriteCount = favorites.length;
        // TODO: Load actual review count from API
        _reviewCount = 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSettingsSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const ProfileSettingsSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Profile',
          style: AppTheme.h3.copyWith(color: AppTheme.gray900),
        ),
        actions: [
          IconButton(
            onPressed: _showSettingsSheet,
            icon: const Icon(FeatherIcons.settings, color: AppTheme.gray700),
          ),
        ],
      ),
      body: Consumer<AuthNotifier>(
        builder: (context, authNotifier, child) {
          final user = authNotifier.user;

          return RefreshIndicator(
            onRefresh: _loadUserStats,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // User Profile Header
                  Container(
                    color: AppTheme.white,
                    padding: const EdgeInsets.all(AppTheme.lg),
                    child: Column(
                      children: [
                        // Avatar and basic info
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 40,
                              backgroundColor: AppTheme.primaryPurple,
                              child: Text(
                                user?.name.substring(0, 1).toUpperCase() ?? 'U',
                                style: AppTheme.h2.copyWith(
                                  color: AppTheme.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: AppTheme.lg),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    user?.name ?? 'Guest User',
                                    style: AppTheme.h4.copyWith(
                                      color: AppTheme.gray900,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: AppTheme.xs),
                                  Text(
                                    user?.email ?? 'Not logged in',
                                    style: AppTheme.bodyMedium.copyWith(
                                      color: AppTheme.gray600,
                                    ),
                                  ),
                                  const SizedBox(height: AppTheme.sm),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: AppTheme.sm,
                                      vertical: AppTheme.xs,
                                    ),
                                    decoration: BoxDecoration(
                                      color: user != null 
                                          ? AppTheme.successGreen.withOpacity(0.1)
                                          : AppTheme.warningOrange.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(AppTheme.xs),
                                    ),
                                    child: Text(
                                      user != null ? 'Verified User' : 'Guest',
                                      style: AppTheme.caption.copyWith(
                                        color: user != null 
                                            ? AppTheme.successGreen
                                            : AppTheme.warningOrange,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppTheme.lg),
                        
                        // Edit Profile Button
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // TODO: Navigate to edit profile screen
                            },
                            icon: const Icon(FeatherIcons.edit2, size: 16),
                            label: const Text('Edit Profile'),
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: AppTheme.primaryPurple),
                              foregroundColor: AppTheme.primaryPurple,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppTheme.sm),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppTheme.lg),

                  // Stats Cards
                  ProfileStatsCard(
                    favoriteCount: _favoriteCount,
                    reviewCount: _reviewCount,
                    isLoading: _isLoading,
                  ),

                  const SizedBox(height: AppTheme.lg),

                  // Menu Sections
                  ProfileMenuSection(
                    title: 'My Activity',
                    items: [
                      ProfileMenuItem(
                        icon: FeatherIcons.heart,
                        title: 'Favorites',
                        subtitle: '$_favoriteCount saved businesses',
                        onTap: () => context.go('/favorites'),
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.star,
                        title: 'My Reviews',
                        subtitle: '$_reviewCount reviews written',
                        onTap: () {
                          // TODO: Navigate to user reviews screen
                        },
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.clock,
                        title: 'Recently Viewed',
                        subtitle: 'See your browsing history',
                        onTap: () {
                          // TODO: Navigate to recently viewed screen
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.lg),

                  ProfileMenuSection(
                    title: 'Preferences',
                    items: [
                      ProfileMenuItem(
                        icon: FeatherIcons.mapPin,
                        title: 'Location Settings',
                        subtitle: 'Manage location preferences',
                        onTap: () => context.go('/location-permission'),
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.bell,
                        title: 'Notifications',
                        subtitle: 'Manage notification settings',
                        onTap: () {
                          // TODO: Navigate to notification settings
                        },
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.shield,
                        title: 'Privacy',
                        subtitle: 'Privacy and data settings',
                        onTap: () {
                          // TODO: Navigate to privacy settings
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.lg),

                  ProfileMenuSection(
                    title: 'Support',
                    items: [
                      ProfileMenuItem(
                        icon: FeatherIcons.helpCircle,
                        title: 'Help & FAQ',
                        subtitle: 'Get help and find answers',
                        onTap: () {
                          // TODO: Navigate to help screen
                        },
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.messageCircle,
                        title: 'Contact Support',
                        subtitle: 'Get in touch with our team',
                        onTap: () {
                          // TODO: Navigate to contact support
                        },
                      ),
                      ProfileMenuItem(
                        icon: FeatherIcons.info,
                        title: 'About',
                        subtitle: 'App version and information',
                        onTap: () {
                          _showAboutDialog();
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.lg),

                  // Account Actions
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
                    child: Column(
                      children: [
                        if (user != null) ...[
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed: () => _showLogoutDialog(),
                              icon: const Icon(FeatherIcons.logOut, size: 16),
                              label: const Text('Sign Out'),
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: AppTheme.errorRed),
                                foregroundColor: AppTheme.errorRed,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppTheme.sm),
                                ),
                              ),
                            ),
                          ),
                        ] else ...[
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () => context.go('/login'),
                              icon: const Icon(FeatherIcons.logIn, size: 16),
                              label: const Text('Sign In'),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: AppTheme.xxxl),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Sign Out',
          style: AppTheme.h4.copyWith(color: AppTheme.gray900),
        ),
        content: Text(
          'Are you sure you want to sign out?',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await context.read<AuthNotifier>().logout();
              if (mounted) {
                context.go('/login');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() async {
    final appInfo = await AppInitializationService.instance.getAppInfo();
    
    if (mounted) {
      showAboutDialog(
        context: context,
        applicationName: 'LocalFind',
        applicationVersion: appInfo['version'],
        applicationIcon: Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: AppTheme.primaryPurple,
            borderRadius: BorderRadius.circular(AppTheme.md),
          ),
          child: const Icon(
            FeatherIcons.mapPin,
            color: AppTheme.white,
            size: 32,
          ),
        ),
        children: [
          Text(
            'Discover local businesses near you with personalized recommendations and authentic reviews.',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
          ),
        ],
      );
    }
  }
}

class ProfileMenuItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const ProfileMenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}
