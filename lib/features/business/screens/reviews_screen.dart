import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

import '../domain/business_detail_model.dart';
import '../domain/review_model.dart';
import '../data/business_repository.dart';
import '../widgets/review_card.dart';
import '../widgets/rating_distribution.dart';
import '../widgets/write_review_sheet.dart';
import '../../auth/providers/auth_notifier.dart';
import '../../../core/theme/app_theme.dart';

class ReviewsScreen extends StatefulWidget {
  final int businessId;
  final String businessName;
  final double averageRating;
  final int reviewCount;

  const ReviewsScreen({
    super.key,
    required this.businessId,
    required this.businessName,
    required this.averageRating,
    required this.reviewCount,
  });

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen> {
  List<ReviewModel> _reviews = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _sortBy = 'newest'; // newest, oldest, highest, lowest
  int _currentPage = 1;
  bool _hasMoreReviews = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadReviews();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadReviews({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _hasMoreReviews = true;
        _reviews.clear();
      });
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final reviews = await context.read<BusinessRepository>().getBusinessReviews(
        widget.businessId,
        page: _currentPage,
        sortBy: _sortBy,
      );

      setState(() {
        if (refresh) {
          _reviews = reviews;
        } else {
          _reviews.addAll(reviews);
        }
        _hasMoreReviews = reviews.length >= 20; // Assuming 20 per page
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading && _hasMoreReviews) {
      _loadReviews();
    }
  }

  void _changeSortOrder(String sortBy) {
    if (_sortBy != sortBy) {
      setState(() {
        _sortBy = sortBy;
      });
      _loadReviews(refresh: true);
    }
  }

  void _showWriteReviewSheet() {
    final authNotifier = context.read<AuthNotifier>();
    if (!authNotifier.isAuthenticated) {
      _showLoginPrompt();
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => WriteReviewSheet(
        businessId: widget.businessId,
        businessName: widget.businessName,
        onReviewSubmitted: () {
          _loadReviews(refresh: true);
        },
      ),
    );
  }

  void _showLoginPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Login Required',
          style: AppTheme.h4.copyWith(color: AppTheme.gray900),
        ),
        content: Text(
          'You need to be logged in to write a review.',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to login screen
              // context.go('/login');
            },
            child: const Text('Login'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Reviews',
          style: AppTheme.h3.copyWith(color: AppTheme.gray900),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: _changeSortOrder,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'newest',
                child: Text('Newest First'),
              ),
              const PopupMenuItem(
                value: 'oldest',
                child: Text('Oldest First'),
              ),
              const PopupMenuItem(
                value: 'highest',
                child: Text('Highest Rating'),
              ),
              const PopupMenuItem(
                value: 'lowest',
                child: Text('Lowest Rating'),
              ),
            ],
            child: Container(
              padding: const EdgeInsets.all(AppTheme.sm),
              child: const Icon(FeatherIcons.moreVertical),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Reviews Summary
          Container(
            color: AppTheme.white,
            padding: const EdgeInsets.all(AppTheme.lg),
            child: Column(
              children: [
                // Overall Rating
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.averageRating.toStringAsFixed(1),
                          style: AppTheme.h1.copyWith(
                            color: AppTheme.gray900,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        RatingBarIndicator(
                          rating: widget.averageRating,
                          itemBuilder: (context, index) => const Icon(
                            Icons.star,
                            color: AppTheme.goldStar,
                          ),
                          itemCount: 5,
                          itemSize: 20,
                        ),
                        const SizedBox(height: AppTheme.xs),
                        Text(
                          '${widget.reviewCount} reviews',
                          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                        ),
                      ],
                    ),
                    const SizedBox(width: AppTheme.xxl),
                    Expanded(
                      child: RatingDistribution(
                        reviews: _reviews,
                        totalReviews: widget.reviewCount,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppTheme.lg),
                
                // Write Review Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _showWriteReviewSheet,
                    icon: const Icon(FeatherIcons.edit3, size: 18),
                    label: const Text('Write a Review'),
                  ),
                ),
              ],
            ),
          ),

          // Reviews List
          Expanded(
            child: _buildReviewsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList() {
    if (_isLoading && _reviews.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryPurple),
      );
    }

    if (_errorMessage != null && _reviews.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.xxxl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FeatherIcons.alertCircle,
                size: 64,
                color: AppTheme.errorRed,
              ),
              const SizedBox(height: AppTheme.lg),
              Text(
                'Failed to load reviews',
                style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
              ),
              const SizedBox(height: AppTheme.sm),
              Text(
                _errorMessage!,
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.lg),
              ElevatedButton(
                onPressed: () => _loadReviews(refresh: true),
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }

    if (_reviews.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.xxxl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FeatherIcons.star,
                size: 64,
                color: AppTheme.gray400,
              ),
              const SizedBox(height: AppTheme.lg),
              Text(
                'No reviews yet',
                style: AppTheme.h4.copyWith(color: AppTheme.gray700),
              ),
              const SizedBox(height: AppTheme.sm),
              Text(
                'Be the first to review ${widget.businessName}',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.lg),
              ElevatedButton.icon(
                onPressed: _showWriteReviewSheet,
                icon: const Icon(FeatherIcons.edit3, size: 18),
                label: const Text('Write First Review'),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadReviews(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: AppTheme.sm),
        itemCount: _reviews.length + (_hasMoreReviews ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _reviews.length) {
            return const Padding(
              padding: EdgeInsets.all(AppTheme.lg),
              child: Center(
                child: CircularProgressIndicator(color: AppTheme.primaryPurple),
              ),
            );
          }

          return ReviewCard(
            review: _reviews[index],
            onHelpfulTap: (reviewId) => _markReviewHelpful(reviewId),
          );
        },
      ),
    );
  }

  Future<void> _markReviewHelpful(int reviewId) async {
    try {
      await context.read<BusinessRepository>().markReviewHelpful(reviewId);
      // Update the review in the list
      setState(() {
        final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
        if (reviewIndex != -1) {
          // TODO: Update helpful count in review model
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to mark review as helpful: $e'),
          backgroundColor: AppTheme.errorRed,
        ),
      );
    }
  }
}
