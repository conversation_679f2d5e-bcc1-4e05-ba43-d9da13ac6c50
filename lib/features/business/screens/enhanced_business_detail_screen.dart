import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/business_detail_model.dart';
import '../data/business_repository.dart';
import '../../profile/providers/favorites_notifier.dart';
import '../widgets/business_hero_gallery.dart';
import '../widgets/business_info_tabs.dart';
import '../widgets/business_quick_actions.dart';
import '../widgets/business_hours_display.dart';
import '../widgets/products_services_display.dart';
import '../../../core/theme/app_theme.dart';

class EnhancedBusinessDetailScreen extends StatefulWidget {
  final String slug;

  const EnhancedBusinessDetailScreen({super.key, required this.slug});

  @override
  State<EnhancedBusinessDetailScreen> createState() => _EnhancedBusinessDetailScreenState();
}

class _EnhancedBusinessDetailScreenState extends State<EnhancedBusinessDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  BusinessDetailModel? _business;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadBusinessDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBusinessDetails() async {
    try {
      final business = await context.read<BusinessRepository>().getBusinessBySlug(widget.slug);
      setState(() {
        _business = business;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.gray100,
        appBar: AppBar(
          backgroundColor: AppTheme.white,
          elevation: 0,
          surfaceTintColor: Colors.transparent,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: AppTheme.primaryPurple),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: AppTheme.gray100,
        appBar: AppBar(
          backgroundColor: AppTheme.white,
          elevation: 0,
          surfaceTintColor: Colors.transparent,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.xxxl),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  FeatherIcons.alertCircle,
                  size: 64,
                  color: AppTheme.errorRed,
                ),
                const SizedBox(height: AppTheme.lg),
                Text(
                  'Business Not Found',
                  style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
                ),
                const SizedBox(height: AppTheme.sm),
                Text(
                  _errorMessage!,
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.lg),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_business == null) {
      return Scaffold(
        backgroundColor: AppTheme.gray100,
        appBar: AppBar(
          backgroundColor: AppTheme.white,
          elevation: 0,
          surfaceTintColor: Colors.transparent,
        ),
        body: const Center(
          child: Text('Business not found'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.gray100,
      body: CustomScrollView(
        slivers: [
          // Hero Gallery App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: AppTheme.white,
            surfaceTintColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: BusinessHeroGallery(business: _business!),
            ),
            actions: [
              // Share Button
              IconButton(
                onPressed: () => _shareBusinesss(_business!),
                icon: const Icon(FeatherIcons.share2, color: AppTheme.white),
              ),
              // Favorite Button
              Consumer<FavoritesNotifier>(
                builder: (context, favoritesNotifier, child) {
                  final isFavorite = favoritesNotifier.isFavorite(_business!.id);
                  return IconButton(
                    onPressed: () => favoritesNotifier.toggleFavorite(_business!.id),
                    icon: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? AppTheme.heartRed : AppTheme.white,
                    ),
                  );
                },
              ),
            ],
          ),

          // Business Info Header
          SliverToBoxAdapter(
            child: Container(
              color: AppTheme.white,
              padding: const EdgeInsets.all(AppTheme.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Business Name
                  Text(
                    _business!.name,
                    style: AppTheme.h2.copyWith(color: AppTheme.gray900),
                  ),
                  const SizedBox(height: AppTheme.sm),
                  
                  // Rating and Reviews
                  Row(
                    children: [
                      RatingBarIndicator(
                        rating: _business!.averageRating,
                        itemBuilder: (context, index) => const Icon(
                          Icons.star,
                          color: AppTheme.goldStar,
                        ),
                        itemCount: 5,
                        itemSize: 20,
                      ),
                      const SizedBox(width: AppTheme.sm),
                      Text(
                        '${_business!.averageRating.toStringAsFixed(1)}',
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.gray900,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Text(
                        '(${_business!.reviewCount} reviews)',
                        style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.sm),
                  
                  // Location and Status
                  Row(
                    children: [
                      const Icon(
                        FeatherIcons.mapPin,
                        size: 16,
                        color: AppTheme.gray500,
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Expanded(
                        child: Text(
                          _business!.address,
                          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
                        ),
                      ),
                      if (_business!.distanceKm != null) ...[
                        const SizedBox(width: AppTheme.sm),
                        Text(
                          '${_business!.distanceKm!.toStringAsFixed(1)} km',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.distanceBlue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: AppTheme.sm),
                  
                  // Price Range and Categories
                  Row(
                    children: [
                      Text(
                        _business!.priceRange,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.gray900,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (_business!.primaryCategory != null) ...[
                        const SizedBox(width: AppTheme.sm),
                        Text(
                          '• ${_business!.primaryCategory}',
                          style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
                        ),
                      ],
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.sm,
                          vertical: AppTheme.xs,
                        ),
                        decoration: BoxDecoration(
                          color: _business!.isOpenNow 
                              ? AppTheme.successGreen 
                              : AppTheme.errorRed,
                          borderRadius: BorderRadius.circular(AppTheme.xs),
                        ),
                        child: Text(
                          _business!.isOpenNow ? 'Open Now' : 'Closed',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Quick Actions
          SliverToBoxAdapter(
            child: BusinessQuickActions(business: _business!),
          ),

          // Information Tabs
          SliverToBoxAdapter(
            child: BusinessInfoTabs(
              business: _business!,
              tabController: _tabController,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _shareBusinesss(BusinessDetailModel business) async {
    // TODO: Implement share functionality
    // This would typically use the share_plus package
  }
}
