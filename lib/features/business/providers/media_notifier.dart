import 'package:flutter/foundation.dart';
import 'dart:io';
import '../data/media_repository.dart';
import '../domain/business_media_model.dart';

class MediaNotifier extends ChangeNotifier {
  final MediaRepository _mediaRepository;

  bool _isLoading = false;
  String? _errorMessage;
  List<BusinessMediaModel> _mediaList = [];
  bool _isUploading = false;

  MediaNotifier(this._mediaRepository);

  // Getters
  bool get isLoading => _isLoading;
  bool get isUploading => _isUploading;
  String? get errorMessage => _errorMessage;
  List<BusinessMediaModel> get mediaList => _mediaList;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Upload media
  Future<bool> uploadMedia({
    required File mediaFile,
    required String mediaType,
    String? caption,
  }) async {
    _isUploading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final uploadedMedia = await _mediaRepository.uploadMedia(
        mediaFile: mediaFile,
        mediaType: mediaType,
        caption: caption,
      );

      // Add to the beginning of the list
      _mediaList.insert(0, uploadedMedia);
      _isUploading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isUploading = false;
      notifyListeners();
      return false;
    }
  }

  // Load business media
  Future<void> loadBusinessMedia() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _mediaList = await _mediaRepository.getBusinessMedia();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete media
  Future<bool> deleteMedia(int mediaId) async {
    try {
      await _mediaRepository.deleteMedia(mediaId);
      
      // Remove from local list
      _mediaList.removeWhere((media) => media.id == mediaId);
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      notifyListeners();
      return false;
    }
  }

  // Reset state
  void reset() {
    _isLoading = false;
    _isUploading = false;
    _errorMessage = null;
    _mediaList.clear();
    notifyListeners();
  }
}
