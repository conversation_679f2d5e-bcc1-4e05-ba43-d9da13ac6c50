import 'package:flutter/foundation.dart';
import '../data/review_repository.dart';
import '../domain/review_model.dart';

class ReviewNotifier extends ChangeNotifier {
  final ReviewRepository _reviewRepository;

  bool _isLoading = false;
  String? _errorMessage;
  ReviewModel? _lastCreatedReview;

  ReviewNotifier(this._reviewRepository);

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  ReviewModel? get lastCreatedReview => _lastCreatedReview;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Create a new review
  Future<bool> createReview({
    required int businessId,
    required int rating,
    required String comment,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final review = await _reviewRepository.createReview(
        businessId: businessId,
        rating: rating,
        comment: comment,
      );

      _lastCreatedReview = review;
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Reply to a review (for business owners)
  Future<bool> replyToReview({
    required int reviewId,
    required String reply,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await _reviewRepository.replyToReview(
        reviewId: reviewId,
        reply: reply,
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Reset state
  void reset() {
    _isLoading = false;
    _errorMessage = null;
    _lastCreatedReview = null;
    notifyListeners();
  }
}
