import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/business_detail_model.dart';
import '../../../core/theme/app_theme.dart';

class BusinessHeroGallery extends StatefulWidget {
  final BusinessDetailModel business;

  const BusinessHeroGallery({
    super.key,
    required this.business,
  });

  @override
  State<BusinessHeroGallery> createState() => _BusinessHeroGalleryState();
}

class _BusinessHeroGalleryState extends State<BusinessHeroGallery> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  List<String> get _imageUrls {
    final urls = <String>[];
    
    // Add logo as first image if available
    if (widget.business.logoUrl != null) {
      urls.add(widget.business.logoUrl!);
    }
    
    // Add media images
    for (final media in widget.business.media) {
      if (media.type == 'image' && media.url.isNotEmpty) {
        urls.add(media.url);
      }
    }
    
    // If no images, return empty list (will show placeholder)
    return urls;
  }

  @override
  Widget build(BuildContext context) {
    final imageUrls = _imageUrls;
    
    if (imageUrls.isEmpty) {
      return _buildPlaceholder();
    }

    return Stack(
      children: [
        // Image Gallery
        PageView.builder(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          itemCount: imageUrls.length,
          itemBuilder: (context, index) {
            return _buildImageItem(imageUrls[index]);
          },
        ),

        // Gradient overlay for better text readability
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.3),
                  Colors.transparent,
                  Colors.black.withOpacity(0.5),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
        ),

        // Page indicators
        if (imageUrls.length > 1)
          Positioned(
            bottom: AppTheme.lg,
            left: 0,
            right: 0,
            child: _buildPageIndicators(imageUrls.length),
          ),

        // Image counter
        if (imageUrls.length > 1)
          Positioned(
            top: AppTheme.lg + MediaQuery.of(context).padding.top,
            right: AppTheme.lg,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.sm,
                vertical: AppTheme.xs,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(AppTheme.lg),
              ),
              child: Text(
                '${_currentIndex + 1} / ${imageUrls.length}',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

        // View all photos button
        if (imageUrls.length > 1)
          Positioned(
            bottom: AppTheme.lg,
            right: AppTheme.lg,
            child: GestureDetector(
              onTap: () => _showFullGallery(context, imageUrls),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.md,
                  vertical: AppTheme.sm,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(AppTheme.lg),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      FeatherIcons.image,
                      color: AppTheme.white,
                      size: 16,
                    ),
                    const SizedBox(width: AppTheme.xs),
                    Text(
                      'View All',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageItem(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: AppTheme.gray300,
        child: const Center(
          child: CircularProgressIndicator(
            color: AppTheme.white,
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) => _buildPlaceholder(),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: AppTheme.gray300,
      child: const Center(
        child: Icon(
          FeatherIcons.briefcase,
          size: 64,
          color: AppTheme.gray500,
        ),
      ),
    );
  }

  Widget _buildPageIndicators(int count) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? AppTheme.white
                : AppTheme.white.withOpacity(0.5),
          ),
        );
      }),
    );
  }

  void _showFullGallery(BuildContext context, List<String> imageUrls) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullGalleryScreen(
          imageUrls: imageUrls,
          initialIndex: _currentIndex,
          businessName: widget.business.name,
        ),
      ),
    );
  }
}

class FullGalleryScreen extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;
  final String businessName;

  const FullGalleryScreen({
    super.key,
    required this.imageUrls,
    required this.initialIndex,
    required this.businessName,
  });

  @override
  State<FullGalleryScreen> createState() => _FullGalleryScreenState();
}

class _FullGalleryScreenState extends State<FullGalleryScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: AppTheme.white,
        title: Text(
          widget.businessName,
          style: AppTheme.h5.copyWith(color: AppTheme.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement share image functionality
            },
            icon: const Icon(FeatherIcons.share2),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Full screen image gallery
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.imageUrls.length,
            itemBuilder: (context, index) {
              return InteractiveViewer(
                child: CachedNetworkImage(
                  imageUrl: widget.imageUrls[index],
                  fit: BoxFit.contain,
                  placeholder: (context, url) => const Center(
                    child: CircularProgressIndicator(color: AppTheme.white),
                  ),
                  errorWidget: (context, url, error) => const Center(
                    child: Icon(
                      FeatherIcons.image,
                      size: 64,
                      color: AppTheme.gray500,
                    ),
                  ),
                ),
              );
            },
          ),

          // Image counter
          Positioned(
            bottom: AppTheme.lg + MediaQuery.of(context).padding.bottom,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.md,
                  vertical: AppTheme.sm,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(AppTheme.lg),
                ),
                child: Text(
                  '${_currentIndex + 1} of ${widget.imageUrls.length}',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
