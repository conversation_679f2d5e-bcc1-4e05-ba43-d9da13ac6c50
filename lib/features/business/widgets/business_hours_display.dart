import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/business_hour_model.dart';
import '../../../core/theme/app_theme.dart';

class BusinessHoursDisplay extends StatefulWidget {
  final List<BusinessHourModel> hours;
  final bool isCompact;

  const BusinessHoursDisplay({
    super.key,
    required this.hours,
    this.isCompact = false,
  });

  @override
  State<BusinessHoursDisplay> createState() => _BusinessHoursDisplayState();
}

class _BusinessHoursDisplayState extends State<BusinessHoursDisplay> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    if (widget.hours.isEmpty) {
      return _buildNoHoursMessage();
    }

    final sortedHours = _getSortedHours();
    final currentDay = DateTime.now().weekday;
    final todayHours = _getTodayHours(sortedHours, currentDay);

    if (widget.isCompact) {
      return _buildCompactView(todayHours);
    }

    return _buildFullView(sortedHours, currentDay);
  }

  Widget _buildNoHoursMessage() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Row(
        children: [
          const Icon(
            FeatherIcons.clock,
            color: AppTheme.gray500,
            size: 20,
          ),
          const SizedBox(width: AppTheme.sm),
          Text(
            'Hours not available',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactView(BusinessHourModel? todayHours) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.sm,
      ),
      child: Row(
        children: [
          const Icon(
            FeatherIcons.clock,
            color: AppTheme.gray700,
            size: 16,
          ),
          const SizedBox(width: AppTheme.sm),
          if (todayHours != null) ...[
            Text(
              _getStatusText(todayHours),
              style: AppTheme.bodySmall.copyWith(
                color: _getStatusColor(todayHours),
                fontWeight: FontWeight.w600,
              ),
            ),
            if (todayHours.openTime != null && todayHours.closeTime != null) ...[
              const SizedBox(width: AppTheme.xs),
              Text(
                '• ${_formatTime(todayHours.openTime!)} - ${_formatTime(todayHours.closeTime!)}',
                style: AppTheme.bodySmall.copyWith(color: AppTheme.gray700),
              ),
            ],
          ] else ...[
            Text(
              'Hours not available',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullView(List<BusinessHourModel> sortedHours, int currentDay) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with expand/collapse button
          Row(
            children: [
              const Icon(
                FeatherIcons.clock,
                color: AppTheme.gray700,
                size: 20,
              ),
              const SizedBox(width: AppTheme.sm),
              Text(
                'Hours',
                style: AppTheme.h5.copyWith(color: AppTheme.gray900),
              ),
              const Spacer(),
              if (sortedHours.length > 1)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _isExpanded ? 'Show Less' : 'Show All',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.primaryPurple,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Icon(
                        _isExpanded ? FeatherIcons.chevronUp : FeatherIcons.chevronDown,
                        color: AppTheme.primaryPurple,
                        size: 16,
                      ),
                    ],
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: AppTheme.md),
          
          // Hours list
          if (_isExpanded || sortedHours.length == 1) ...[
            ...sortedHours.map((hour) => _buildHourRow(hour, currentDay)),
          ] else ...[
            // Show only today's hours
            _buildHourRow(_getTodayHours(sortedHours, currentDay) ?? sortedHours.first, currentDay),
          ],
        ],
      ),
    );
  }

  Widget _buildHourRow(BusinessHourModel hour, int currentDay) {
    final isToday = hour.dayOfWeek == currentDay;
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.xs),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              _getDayName(hour.dayOfWeek),
              style: AppTheme.bodyMedium.copyWith(
                color: isToday ? AppTheme.primaryPurple : AppTheme.gray700,
                fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.md),
          Expanded(
            child: Text(
              _getHoursText(hour),
              style: AppTheme.bodyMedium.copyWith(
                color: isToday ? AppTheme.gray900 : AppTheme.gray700,
                fontWeight: isToday ? FontWeight.w500 : FontWeight.w400,
              ),
            ),
          ),
          if (isToday) ...[
            const SizedBox(width: AppTheme.sm),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.xs,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(hour),
                borderRadius: BorderRadius.circular(AppTheme.xs),
              ),
              child: Text(
                _getStatusText(hour),
                style: AppTheme.caption.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<BusinessHourModel> _getSortedHours() {
    final hours = List<BusinessHourModel>.from(widget.hours);
    hours.sort((a, b) => a.dayOfWeek.compareTo(b.dayOfWeek));
    return hours;
  }

  BusinessHourModel? _getTodayHours(List<BusinessHourModel> hours, int currentDay) {
    try {
      return hours.firstWhere((hour) => hour.dayOfWeek == currentDay);
    } catch (e) {
      return null;
    }
  }

  String _getDayName(int dayOfWeek) {
    const days = [
      'Monday',
      'Tuesday', 
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return days[dayOfWeek - 1];
  }

  String _getHoursText(BusinessHourModel hour) {
    if (hour.isClosed) {
      return 'Closed';
    }
    
    if (hour.openTime != null && hour.closeTime != null) {
      return '${_formatTime(hour.openTime!)} - ${_formatTime(hour.closeTime!)}';
    }
    
    return 'Hours not available';
  }

  String _formatTime(String time) {
    try {
      final parts = time.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      
      return '${displayHour}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  String _getStatusText(BusinessHourModel hour) {
    if (hour.isClosed) {
      return 'Closed';
    }
    
    // TODO: Implement real-time open/closed status based on current time
    // For now, just return "Open" if not closed
    return 'Open';
  }

  Color _getStatusColor(BusinessHourModel hour) {
    if (hour.isClosed) {
      return AppTheme.errorRed;
    }
    
    // TODO: Implement real-time status checking
    return AppTheme.successGreen;
  }
}
