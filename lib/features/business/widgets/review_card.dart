import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../domain/review_model.dart';
import '../../../core/theme/app_theme.dart';

class ReviewCard extends StatelessWidget {
  final ReviewModel review;
  final Function(int)? onHelpfulTap;
  final Function(int)? onReportTap;

  const ReviewCard({
    super.key,
    required this.review,
    this.onHelpfulTap,
    this.onReportTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
      padding: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info and rating
          Row(
            children: [
              // User avatar
              CircleAvatar(
                radius: 20,
                backgroundColor: AppTheme.primaryPurple,
                child: Text(
                  _getUserInitial(),
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.sm),
              
              // User name and date
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.user.name,
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.gray900,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      _getFormattedDate(),
                      style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                    ),
                  ],
                ),
              ),
              
              // Rating stars
              RatingBarIndicator(
                rating: review.rating.toDouble(),
                itemBuilder: (context, index) => const Icon(
                  Icons.star,
                  color: AppTheme.goldStar,
                ),
                itemCount: 5,
                itemSize: 16,
              ),
            ],
          ),
          
          const SizedBox(height: AppTheme.md),
          
          // Review comment
          Text(
            review.comment,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
          ),
          
          const SizedBox(height: AppTheme.md),
          
          // Action buttons
          Row(
            children: [
              // Helpful button
              GestureDetector(
                onTap: onHelpfulTap != null ? () => onHelpfulTap!(review.id) : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.sm,
                    vertical: AppTheme.xs,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.gray100,
                    borderRadius: BorderRadius.circular(AppTheme.lg),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        FeatherIcons.thumbsUp,
                        size: 14,
                        color: AppTheme.gray600,
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Text(
                        'Helpful',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.gray600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: AppTheme.sm),
              
              // Report button
              GestureDetector(
                onTap: onReportTap != null ? () => onReportTap!(review.id) : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.sm,
                    vertical: AppTheme.xs,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.gray100,
                    borderRadius: BorderRadius.circular(AppTheme.lg),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        FeatherIcons.flag,
                        size: 14,
                        color: AppTheme.gray600,
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Text(
                        'Report',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.gray600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const Spacer(),
              
              // More options
              IconButton(
                onPressed: () => _showMoreOptions(context),
                icon: const Icon(
                  FeatherIcons.moreHorizontal,
                  size: 16,
                  color: AppTheme.gray500,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          
          // Owner reply
          if (review.ownerReply != null && review.ownerReply!.isNotEmpty) ...[
            const SizedBox(height: AppTheme.md),
            Container(
              padding: const EdgeInsets.all(AppTheme.md),
              decoration: BoxDecoration(
                color: AppTheme.gray100,
                borderRadius: BorderRadius.circular(AppTheme.sm),
                border: Border.all(color: AppTheme.gray300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        FeatherIcons.messageCircle,
                        size: 16,
                        color: AppTheme.primaryPurple,
                      ),
                      const SizedBox(width: AppTheme.xs),
                      Text(
                        'Response from owner',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.primaryPurple,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppTheme.sm),
                  Text(
                    review.ownerReply!,
                    style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getUserInitial() {
    if (review.user.name.isNotEmpty) {
      return review.user.name[0].toUpperCase();
    }
    return 'U';
  }

  String _getFormattedDate() {
    try {
      final date = DateTime.parse(review.createdAt);
      return timeago.format(date);
    } catch (e) {
      return review.createdAt;
    }
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(AppTheme.lg)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppTheme.md),
              decoration: BoxDecoration(
                color: AppTheme.gray300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Options
            ListTile(
              leading: const Icon(FeatherIcons.share2, color: AppTheme.gray700),
              title: const Text('Share Review'),
              onTap: () {
                Navigator.of(context).pop();
                _shareReview();
              },
            ),
            
            ListTile(
              leading: const Icon(FeatherIcons.flag, color: AppTheme.errorRed),
              title: Text(
                'Report Review',
                style: TextStyle(color: AppTheme.errorRed),
              ),
              onTap: () {
                Navigator.of(context).pop();
                if (onReportTap != null) {
                  onReportTap!(review.id);
                }
              },
            ),
            
            const SizedBox(height: AppTheme.lg),
          ],
        ),
      ),
    );
  }

  void _shareReview() {
    // TODO: Implement share functionality
  }
}

class ReviewSkeleton extends StatelessWidget {
  const ReviewSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
      padding: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: AppTheme.gray300,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: AppTheme.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 100,
                      height: 16,
                      decoration: BoxDecoration(
                        color: AppTheme.gray300,
                        borderRadius: BorderRadius.circular(AppTheme.xs),
                      ),
                    ),
                    const SizedBox(height: AppTheme.xs),
                    Container(
                      width: 80,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppTheme.gray300,
                        borderRadius: BorderRadius.circular(AppTheme.xs),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 80,
                height: 16,
                decoration: BoxDecoration(
                  color: AppTheme.gray300,
                  borderRadius: BorderRadius.circular(AppTheme.xs),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.md),
          Container(
            width: double.infinity,
            height: 60,
            decoration: BoxDecoration(
              color: AppTheme.gray300,
              borderRadius: BorderRadius.circular(AppTheme.xs),
            ),
          ),
          const SizedBox(height: AppTheme.md),
          Row(
            children: [
              Container(
                width: 60,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.gray300,
                  borderRadius: BorderRadius.circular(AppTheme.lg),
                ),
              ),
              const SizedBox(width: AppTheme.sm),
              Container(
                width: 50,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.gray300,
                  borderRadius: BorderRadius.circular(AppTheme.lg),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
