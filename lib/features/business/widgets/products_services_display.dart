import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/product_model.dart';
import '../domain/service_model.dart';
import '../../../core/theme/app_theme.dart';

class ProductsServicesDisplay extends StatefulWidget {
  final List<ProductModel> products;
  final List<ServiceModel> services;
  final bool showProducts;

  const ProductsServicesDisplay({
    super.key,
    required this.products,
    required this.services,
    this.showProducts = true,
  });

  @override
  State<ProductsServicesDisplay> createState() =>
      _ProductsServicesDisplayState();
}

class _ProductsServicesDisplayState extends State<ProductsServicesDisplay> {
  String _selectedCategory = 'All';
  bool _isGridView = false;

  @override
  Widget build(BuildContext context) {
    final items = widget.showProducts ? widget.products : widget.services;
    final categories = _getCategories(items);
    final filteredItems = _getFilteredItems(items);

    return Column(
      children: [
        // Header with view toggle and category filter
        Container(
          padding: const EdgeInsets.all(AppTheme.lg),
          decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: AppTheme.gray300)),
          ),
          child: Column(
            children: [
              // View toggle and search
              Row(
                children: [
                  Text(
                    widget.showProducts ? 'Menu Items' : 'Services',
                    style: AppTheme.h5.copyWith(color: AppTheme.gray900),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isGridView = !_isGridView;
                      });
                    },
                    icon: Icon(
                      _isGridView ? FeatherIcons.list : FeatherIcons.grid,
                      color: AppTheme.gray700,
                    ),
                  ),
                ],
              ),

              // Category filter
              if (categories.length > 1) ...[
                const SizedBox(height: AppTheme.md),
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      final isSelected = _selectedCategory == category;

                      return Container(
                        margin: const EdgeInsets.only(right: AppTheme.sm),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                          backgroundColor: AppTheme.gray100,
                          selectedColor: AppTheme.primaryPurple,
                          labelStyle: TextStyle(
                            color: isSelected
                                ? AppTheme.white
                                : AppTheme.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),

        // Items list/grid
        Expanded(
          child: filteredItems.isEmpty
              ? _buildEmptyState()
              : _isGridView
              ? _buildGridView(filteredItems)
              : _buildListView(filteredItems),
        ),
      ],
    );
  }

  List<String> _getCategories(List<dynamic> items) {
    final categories = <String>{'All'};

    for (final item in items) {
      if (item is ProductModel && item.category != null) {
        categories.add(item.category!);
      } else if (item is ServiceModel && item.category != null) {
        categories.add(item.category!);
      }
    }

    return categories.toList();
  }

  List<dynamic> _getFilteredItems(List<dynamic> items) {
    if (_selectedCategory == 'All') {
      return items;
    }

    return items.where((item) {
      if (item is ProductModel) {
        return item.category == _selectedCategory;
      } else if (item is ServiceModel) {
        return item.category == _selectedCategory;
      }
      return false;
    }).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.showProducts
                  ? FeatherIcons.package
                  : FeatherIcons.settings,
              size: 64,
              color: AppTheme.gray400,
            ),
            const SizedBox(height: AppTheme.lg),
            Text(
              widget.showProducts ? 'No menu items' : 'No services',
              style: AppTheme.h5.copyWith(color: AppTheme.gray700),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              widget.showProducts
                  ? 'This business hasn\'t added any menu items yet'
                  : 'This business hasn\'t added any services yet',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListView(List<dynamic> items) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.lg),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildListItem(item);
      },
    );
  }

  Widget _buildGridView(List<dynamic> items) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.lg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: AppTheme.md,
        mainAxisSpacing: AppTheme.md,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildGridItem(item);
      },
    );
  }

  Widget _buildListItem(dynamic item) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.md),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.md),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            _buildItemImage(item, size: 80),
            const SizedBox(width: AppTheme.md),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getItemName(item),
                    style: AppTheme.h5.copyWith(color: AppTheme.gray900),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (_getItemDescription(item) != null) ...[
                    const SizedBox(height: AppTheme.xs),
                    Text(
                      _getItemDescription(item)!,
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.gray500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: AppTheme.sm),
                  Row(
                    children: [
                      Text(
                        _getItemPrice(item),
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.primaryPurple,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (item is ServiceModel &&
                          item.formattedDuration != null) ...[
                        const SizedBox(width: AppTheme.sm),
                        Text(
                          '• ${item.formattedDuration}',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.gray500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridItem(dynamic item) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          Expanded(
            flex: 3,
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(AppTheme.md),
              ),
              child: _buildItemImage(item, fit: BoxFit.cover),
            ),
          ),

          // Content
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.sm),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getItemName(item),
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.gray900,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  Text(
                    _getItemPrice(item),
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryPurple,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemImage(dynamic item, {double? size, BoxFit? fit}) {
    final imageUrl = _getItemImageUrl(item);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppTheme.gray100,
        borderRadius: BorderRadius.circular(AppTheme.sm),
      ),
      child: imageUrl != null
          ? CachedNetworkImage(
              imageUrl: imageUrl,
              fit: fit ?? BoxFit.cover,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.primaryPurple,
                  strokeWidth: 2,
                ),
              ),
              errorWidget: (context, url, error) => Icon(
                widget.showProducts
                    ? FeatherIcons.package
                    : FeatherIcons.settings,
                color: AppTheme.gray500,
                size: size != null ? size * 0.4 : 24,
              ),
            )
          : Icon(
              widget.showProducts
                  ? FeatherIcons.package
                  : FeatherIcons.settings,
              color: AppTheme.gray500,
              size: size != null ? size * 0.4 : 24,
            ),
    );
  }

  String _getItemName(dynamic item) {
    if (item is ProductModel) return item.name;
    if (item is ServiceModel) return item.name;
    return '';
  }

  String? _getItemDescription(dynamic item) {
    if (item is ProductModel) return item.description;
    if (item is ServiceModel) return item.description;
    return null;
  }

  String _getItemPrice(dynamic item) {
    if (item is ProductModel) return item.formattedPrice;
    if (item is ServiceModel) return item.formattedPrice;
    return '';
  }

  String? _getItemImageUrl(dynamic item) {
    if (item is ProductModel) return item.imageUrl;
    if (item is ServiceModel) return item.imageUrl;
    return null;
  }
}
