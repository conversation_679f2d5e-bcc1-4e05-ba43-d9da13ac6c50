import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:url_launcher/url_launcher.dart';

import '../domain/business_detail_model.dart';
import '../../../core/theme/app_theme.dart';

class BusinessQuickActions extends StatelessWidget {
  final BusinessDetailModel business;

  const BusinessQuickActions({
    super.key,
    required this.business,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.white,
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Row(
        children: [
          // Call button
          if (business.phoneNumber.isNotEmpty)
            Expanded(
              child: _QuickActionButton(
                icon: FeatherIcons.phone,
                label: 'Call',
                onTap: () => _makePhoneCall(business.phoneNumber),
              ),
            ),

          if (business.phoneNumber.isNotEmpty)
            const SizedBox(width: AppTheme.md),

          // Website button
          if (business.websiteUrl != null && business.websiteUrl!.isNotEmpty)
            Expanded(
              child: _QuickActionButton(
                icon: FeatherIcons.globe,
                label: 'Website',
                onTap: () => _openWebsite(business.websiteUrl!),
              ),
            ),

          if (business.websiteUrl != null && business.websiteUrl!.isNotEmpty)
            const SizedBox(width: AppTheme.md),

          // Directions button
          if (business.latitude != null && business.longitude != null)
            Expanded(
              child: _QuickActionButton(
                icon: FeatherIcons.navigation,
                label: 'Directions',
                onTap: () => _openDirections(business),
              ),
            ),

          if (business.latitude != null && business.longitude != null)
            const SizedBox(width: AppTheme.md),

          // Share button
          Expanded(
            child: _QuickActionButton(
              icon: FeatherIcons.share2,
              label: 'Share',
              onTap: () => _shareBusiness(business),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  Future<void> _openWebsite(String websiteUrl) async {
    final uri = Uri.parse(websiteUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openDirections(BusinessDetailModel business) async {
    if (business.latitude != null && business.longitude != null) {
      final uri = Uri(
        scheme: 'https',
        host: 'maps.google.com',
        path: '/maps',
        queryParameters: {
          'daddr': '${business.latitude},${business.longitude}',
          'directionsmode': 'driving',
        },
      );
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    }
  }

  Future<void> _shareBusiness(BusinessDetailModel business) async {
    // TODO: Implement share functionality using share_plus package
    // For now, just copy to clipboard or show a message
  }
}

class _QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.sm,
          vertical: AppTheme.md,
        ),
        decoration: BoxDecoration(
          color: AppTheme.gray100,
          borderRadius: BorderRadius.circular(AppTheme.md),
          border: Border.all(color: AppTheme.gray300),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24,
              color: AppTheme.primaryPurple,
            ),
            const SizedBox(height: AppTheme.xs),
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.gray900,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
