import 'package:dio/dio.dart';
import '../domain/business_summary_model.dart';
import '../domain/business_detail_model.dart';
import '../domain/review_model.dart';
import '../../discovery/domain/category_model.dart';

class BusinessRepository {
  final Dio _dio;

  BusinessRepository(this._dio);

  Future<List<BusinessSummaryModel>> getBusinesses({
    String? query,
    String? category, // Changed to string slug instead of int ID
    double? latitude,
    double? longitude,
    int? rating,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (query != null && query.isNotEmpty) {
        queryParams['search'] = query;
      }
      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }
      if (rating != null) {
        queryParams['rating'] = rating;
      }
      if (latitude != null && longitude != null) {
        queryParams['lat'] = latitude;
        queryParams['lng'] = longitude;
      }

      final response = await _dio.get(
        '/businesses',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map(
              (business) => BusinessSummaryModel.fromJson(
                business as Map<String, dynamic>,
              ),
            )
            .toList();
      } else {
        throw Exception('Failed to load businesses');
      }
    } catch (e) {
      throw Exception('Failed to load businesses: $e');
    }
  }

  // Review-related methods
  Future<List<ReviewModel>> getBusinessReviews(
    int businessId, {
    int page = 1,
    int limit = 20,
    String sortBy = 'newest',
  }) async {
    try {
      final response = await _dio.get(
        '/businesses/$businessId/reviews',
        queryParameters: {'page': page, 'limit': limit, 'sort': sortBy},
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map(
              (review) => ReviewModel.fromJson(review as Map<String, dynamic>),
            )
            .toList();
      } else {
        throw Exception('Failed to load reviews');
      }
    } catch (e) {
      throw Exception('Failed to load reviews: $e');
    }
  }

  Future<void> submitReview({
    required int businessId,
    required int rating,
    required String comment,
  }) async {
    try {
      final response = await _dio.post(
        '/businesses/$businessId/reviews',
        data: {'rating': rating, 'comment': comment},
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to submit review');
      }
    } catch (e) {
      throw Exception('Failed to submit review: $e');
    }
  }

  Future<void> markReviewHelpful(int reviewId) async {
    try {
      final response = await _dio.post('/reviews/$reviewId/helpful');

      if (response.statusCode != 200) {
        throw Exception('Failed to mark review as helpful');
      }
    } catch (e) {
      throw Exception('Failed to mark review as helpful: $e');
    }
  }

  Future<void> reportReview(int reviewId, String reason) async {
    try {
      final response = await _dio.post(
        '/reviews/$reviewId/report',
        data: {'reason': reason},
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to report review');
      }
    } catch (e) {
      throw Exception('Failed to report review: $e');
    }
  }

  // Enhanced search method with comprehensive filtering
  Future<List<BusinessSummaryModel>> searchBusinesses(
    Map<String, dynamic> params,
  ) async {
    try {
      // Add default pagination if not provided
      params.putIfAbsent('page', () => 1);
      params.putIfAbsent('limit', () => 20);

      final response = await _dio.get('/businesses', queryParameters: params);

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map(
              (business) => BusinessSummaryModel.fromJson(
                business as Map<String, dynamic>,
              ),
            )
            .toList();
      } else {
        throw Exception('Failed to load businesses');
      }
    } on DioException {
      throw Exception('Network error. Please try again.');
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<BusinessDetailModel> getBusinessDetails(String slug) async {
    try {
      final response = await _dio.get('/businesses/$slug');

      if (response.statusCode == 200) {
        return BusinessDetailModel.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
      } else {
        throw Exception('Business not found');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('Business not found');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await _dio.get('/categories');

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map(
              (category) =>
                  CategoryModel.fromJson(category as Map<String, dynamic>),
            )
            .toList();
      } else {
        throw Exception('Failed to load categories');
      }
    } on DioException {
      throw Exception('Network error. Please try again.');
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<List<BusinessSummaryModel>> getNearbyBusinesses({
    required double latitude,
    required double longitude,
    int radius = 10,
    int limit = 10,
  }) async {
    return getBusinesses(
      latitude: latitude,
      longitude: longitude,
      limit: limit,
    );
  }

  /// Alias for getBusinessDetails for compatibility
  Future<BusinessDetailModel> getBusinessBySlug(String slug) async {
    return getBusinessDetails(slug);
  }
}
