import 'package:dio/dio.dart';
import '../domain/review_model.dart';

class ReviewRepository {
  final Dio _dio;

  ReviewRepository(this._dio);

  Future<ReviewModel> createReview({
    required int businessId,
    required int rating,
    required String comment,
  }) async {
    try {
      final response = await _dio.post(
        '/businesses/$businessId/reviews',
        data: {
          'rating': rating,
          'comment': comment,
        },
      );

      if (response.statusCode == 201) {
        return ReviewModel.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
      } else {
        throw Exception('Failed to create review');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to write a review');
      } else if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final firstError = errors.values.first as List;
          throw Exception(firstError.first as String);
        }
        throw Exception('Please check your review information');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Business not found');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<ReviewModel> replyToReview({
    required int reviewId,
    required String reply,
  }) async {
    try {
      final response = await _dio.post(
        '/my-business/reviews/$reviewId/reply',
        data: {
          'owner_reply': reply,
        },
      );

      if (response.statusCode == 200) {
        return ReviewModel.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
      } else {
        throw Exception('Failed to reply to review');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to reply to reviews');
      } else if (e.response?.statusCode == 403) {
        throw Exception('You are not authorized to reply to this review');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Review not found');
      } else if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final firstError = errors.values.first as List;
          throw Exception(firstError.first as String);
        }
        throw Exception('Please check your reply');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }
}
