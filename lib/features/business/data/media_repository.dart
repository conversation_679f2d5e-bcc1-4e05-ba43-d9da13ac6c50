import 'package:dio/dio.dart';
import 'dart:io';
import '../domain/business_media_model.dart';

class MediaRepository {
  final Dio _dio;

  MediaRepository(this._dio);

  Future<BusinessMediaModel> uploadMedia({
    required File mediaFile,
    required String mediaType, // 'image' or 'video'
    String? caption,
  }) async {
    try {
      // Create form data
      final formData = FormData.fromMap({
        'media_file': await MultipartFile.fromFile(
          mediaFile.path,
          filename: mediaFile.path.split('/').last,
        ),
        'media_type': mediaType,
        if (caption != null && caption.isNotEmpty) 'caption': caption,
      });

      final response = await _dio.post(
        '/my-business/media',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 201) {
        return BusinessMediaModel.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
      } else {
        throw Exception('Failed to upload media');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to upload media');
      } else if (e.response?.statusCode == 403) {
        throw Exception('You are not authorized to upload media for this business');
      } else if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final firstError = errors.values.first as List;
          throw Exception(firstError.first as String);
        }
        throw Exception('Please check your media file');
      } else if (e.response?.statusCode == 413) {
        throw Exception('File size too large. Maximum size is 10MB');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<void> deleteMedia(int mediaId) async {
    try {
      final response = await _dio.delete('/my-business/media/$mediaId');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete media');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to delete media');
      } else if (e.response?.statusCode == 403) {
        throw Exception('You are not authorized to delete this media');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Media not found');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<List<BusinessMediaModel>> getBusinessMedia() async {
    try {
      final response = await _dio.get('/my-business/media');

      if (response.statusCode == 200) {
        final data = response.data['data'] as List<dynamic>;
        return data
            .map((media) => BusinessMediaModel.fromJson(media as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Failed to load business media');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Please login to view media');
      } else if (e.response?.statusCode == 403) {
        throw Exception('You are not authorized to view this business media');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }
}
