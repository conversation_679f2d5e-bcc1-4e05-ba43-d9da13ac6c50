class BusinessSummaryModel {
  final int id;
  final String name;
  final String slug;
  final String? logoUrl;
  final String address;
  final double averageRating;
  final int reviewCount;
  final double? distanceKm;
  final String? phoneNumber;
  final String? websiteUrl;
  final String? description;
  final bool isActive;
  final List<String> categories;
  final double? latitude;
  final double? longitude;

  BusinessSummaryModel({
    required this.id,
    required this.name,
    required this.slug,
    this.logoUrl,
    required this.address,
    required this.averageRating,
    required this.reviewCount,
    this.distanceKm,
    this.phoneNumber,
    this.websiteUrl,
    this.description,
    this.isActive = true,
    this.categories = const [],
    this.latitude,
    this.longitude,
  });

  /// Calculate price range from products/services (placeholder for now)
  String get priceRange {
    // This would be calculated from actual products/services data
    // For now, return a placeholder based on review count as a proxy
    if (reviewCount > 100) return '\$\$\$\$';
    if (reviewCount > 50) return '\$\$\$';
    if (reviewCount > 20) return '\$\$';
    return '\$';
  }

  /// Check if business is currently open (placeholder for now)
  bool get isOpenNow {
    // This would be calculated based on business hours and current time
    // For now, return true as placeholder
    return isActive;
  }

  /// Get primary category
  String? get primaryCategory {
    return categories.isNotEmpty ? categories.first : null;
  }

  factory BusinessSummaryModel.fromJson(Map<String, dynamic> json) {
    return BusinessSummaryModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      logoUrl: json['logo_url'] as String?,
      address: json['address'] as String,
      averageRating: (json['average_rating'] as num).toDouble(),
      reviewCount: json['review_count'] as int,
      distanceKm: (json['distance_km'] as num?)?.toDouble(),
      phoneNumber: json['phone_number'] as String?,
      websiteUrl: json['website_url'] as String?,
      description: json['description'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      latitude: double.tryParse(json['latitude'] as String? ?? ''),
      longitude: double.tryParse(json['longitude'] as String? ?? ''),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'logo_url': logoUrl,
      'address': address,
      'average_rating': averageRating,
      'review_count': reviewCount,
      'distance_km': distanceKm,
      'phone_number': phoneNumber,
      'website_url': websiteUrl,
      'description': description,
      'is_active': isActive,
      'categories': categories,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
