import '../../discovery/domain/category_model.dart';
import 'business_hour_model.dart';
import 'business_media_model.dart';
import 'review_model.dart';
import 'product_model.dart';
import 'service_model.dart';

class BusinessDetailModel {
  final int id;
  final String name;
  final String slug;
  final String? logoUrl;
  final String address;
  final double averageRating;
  final int reviewCount;
  final double? distanceKm;
  final String description;
  final String phoneNumber;
  final String? websiteUrl;
  final List<BusinessHourModel> hours;
  final List<BusinessMediaModel> media;
  final List<CategoryModel> categories;
  final List<ReviewModel> reviews;
  final List<ProductModel> products;
  final List<ServiceModel> services;
  final double? latitude;
  final double? longitude;
  final bool isActive;

  BusinessDetailModel({
    required this.id,
    required this.name,
    required this.slug,
    this.logoUrl,
    required this.address,
    required this.averageRating,
    required this.reviewCount,
    this.distanceKm,
    required this.description,
    required this.phoneNumber,
    this.websiteUrl,
    required this.hours,
    required this.media,
    required this.categories,
    required this.reviews,
    this.products = const [],
    this.services = const [],
    this.latitude,
    this.longitude,
    this.isActive = true,
  });

  /// Calculate price range from products and services
  String get priceRange {
    final allPrices = <double>[];

    // Add product prices
    for (final product in products) {
      allPrices.add(product.price);
    }

    // Add service prices
    for (final service in services) {
      if (service.fixedPrice != null) {
        allPrices.add(service.fixedPrice!);
      } else if (service.priceFrom != null) {
        allPrices.add(service.priceFrom!);
      }
      if (service.priceTo != null) {
        allPrices.add(service.priceTo!);
      }
    }

    if (allPrices.isEmpty) return '\$';

    allPrices.sort();
    final maxPrice = allPrices.last;

    if (maxPrice >= 100) return '\$\$\$\$';
    if (maxPrice >= 50) return '\$\$\$';
    if (maxPrice >= 25) return '\$\$';
    return '\$';
  }

  /// Check if business is currently open (placeholder for now)
  bool get isOpenNow {
    // This would be calculated based on business hours and current time
    // For now, return true as placeholder
    return isActive;
  }

  /// Get primary category
  String? get primaryCategory {
    return categories.isNotEmpty ? categories.first.name : null;
  }

  /// Check if business has products
  bool get hasProducts => products.isNotEmpty;

  /// Check if business has services
  bool get hasServices => services.isNotEmpty;

  factory BusinessDetailModel.fromJson(Map<String, dynamic> json) {
    return BusinessDetailModel(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      logoUrl: json['logo_url'] as String?,
      address: json['address'] as String,
      averageRating: (json['average_rating'] as num).toDouble(),
      reviewCount: json['review_count'] as int,
      distanceKm: (json['distance_km'] as num?)?.toDouble(),
      description: json['description'] as String,
      phoneNumber: json['phone_number'] as String,
      websiteUrl: json['website_url'] as String?,
      hours:
          (json['hours'] as List<dynamic>?)
              ?.map(
                (hour) =>
                    BusinessHourModel.fromJson(hour as Map<String, dynamic>),
              )
              .toList() ??
          [],
      media:
          (json['media'] as List<dynamic>?)
              ?.map(
                (media) =>
                    BusinessMediaModel.fromJson(media as Map<String, dynamic>),
              )
              .toList() ??
          [],
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map(
                (category) =>
                    CategoryModel.fromJson(category as Map<String, dynamic>),
              )
              .toList() ??
          [],
      reviews:
          (json['reviews'] as List<dynamic>?)
              ?.map(
                (review) =>
                    ReviewModel.fromJson(review as Map<String, dynamic>),
              )
              .toList() ??
          [],
      products:
          (json['products'] as List<dynamic>?)
              ?.map(
                (product) =>
                    ProductModel.fromJson(product as Map<String, dynamic>),
              )
              .toList() ??
          [],
      services:
          (json['services'] as List<dynamic>?)
              ?.map(
                (service) =>
                    ServiceModel.fromJson(service as Map<String, dynamic>),
              )
              .toList() ??
          [],
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'logo_url': logoUrl,
      'address': address,
      'average_rating': averageRating,
      'review_count': reviewCount,
      'distance_km': distanceKm,
      'description': description,
      'phone_number': phoneNumber,
      'website_url': websiteUrl,
      'hours': hours.map((hour) => hour.toJson()).toList(),
      'media': media.map((m) => m.toJson()).toList(),
      'categories': categories.map((category) => category.toJson()).toList(),
      'reviews': reviews.map((review) => review.toJson()).toList(),
      'products': products.map((product) => product.toJson()).toList(),
      'services': services.map((service) => service.toJson()).toList(),
      'latitude': latitude,
      'longitude': longitude,
      'is_active': isActive,
    };
  }
}
