class BusinessHourModel {
  final int dayOfWeek; // 1-7 (Monday to Sunday) as per Laravel API
  final String openTime; // Format: "09:00"
  final String closeTime; // Format: "17:00"
  final bool isClosed;

  BusinessHourModel({
    required this.dayOfWeek,
    required this.openTime,
    required this.closeTime,
    required this.isClosed,
  }) : assert(
         dayOfWeek >= 1 && dayOfWeek <= 7,
         'dayOfWeek must be between 1 and 7',
       );

  factory BusinessHourModel.fromJson(Map<String, dynamic> json) {
    return BusinessHourModel(
      dayOfWeek: json['day_of_week'] as int,
      openTime: json['open_time'] as String,
      closeTime: json['close_time'] as String,
      isClosed: json['is_closed'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day_of_week': dayOfWeek,
      'open_time': openTime,
      'close_time': closeTime,
      'is_closed': isClosed,
    };
  }

  String get dayName {
    // Laravel API uses 1-7 (Monday to Sunday)
    const days = [
      'Monday', // 1
      'Tuesday', // 2
      'Wednesday', // 3
      'Thursday', // 4
      'Friday', // 5
      'Saturday', // 6
      'Sunday', // 7
    ];

    // Safety check to prevent RangeError
    if (dayOfWeek < 1 || dayOfWeek > 7) {
      return 'Unknown';
    }

    return days[dayOfWeek - 1]; // Convert 1-based to 0-based index
  }

  String get shortDayName {
    // Laravel API uses 1-7 (Monday to Sunday)
    const shortDays = [
      'Mon', // 1
      'Tue', // 2
      'Wed', // 3
      'Thu', // 4
      'Fri', // 5
      'Sat', // 6
      'Sun', // 7
    ];

    // Safety check to prevent RangeError
    if (dayOfWeek < 1 || dayOfWeek > 7) {
      return 'N/A';
    }

    return shortDays[dayOfWeek - 1]; // Convert 1-based to 0-based index
  }

  String get displayTime {
    if (isClosed) return 'Closed';
    return '$openTime - $closeTime';
  }

  bool get isWeekend {
    return dayOfWeek == 6 || dayOfWeek == 7; // Saturday or Sunday
  }

  @override
  String toString() {
    return 'BusinessHourModel(dayOfWeek: $dayOfWeek, dayName: $dayName, displayTime: $displayTime)';
  }
}
