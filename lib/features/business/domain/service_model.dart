class ServiceModel {
  final int id;
  final String name;
  final String? description;
  final String? category;
  final double? priceFrom;
  final double? priceTo;
  final double? fixedPrice;
  final int? durationMinutes;
  final String? imageUrl;
  final bool isAvailable;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  ServiceModel({
    required this.id,
    required this.name,
    this.description,
    this.category,
    this.priceFrom,
    this.priceTo,
    this.fixedPrice,
    this.durationMinutes,
    this.imageUrl,
    this.isAvailable = true,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Format price for display according to the pricing model
  String get formattedPrice {
    if (fixedPrice != null) {
      return '\$${fixedPrice!.toStringAsFixed(2)}';
    } else if (priceFrom != null && priceTo != null) {
      return '\$${priceFrom!.toStringAsFixed(2)} - \$${priceTo!.toStringAsFixed(2)}';
    } else if (priceFrom != null) {
      return 'From \$${priceFrom!.toStringAsFixed(2)}';
    } else if (priceTo != null) {
      return 'Up to \$${priceTo!.toStringAsFixed(2)}';
    } else {
      return 'Price on request';
    }
  }

  /// Get duration display text
  String? get formattedDuration {
    if (durationMinutes == null) return null;
    
    if (durationMinutes! < 60) {
      return '${durationMinutes!} min';
    } else {
      final hours = durationMinutes! ~/ 60;
      final minutes = durationMinutes! % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}min';
      }
    }
  }

  /// Check if service has an image
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  /// Check if service has pricing information
  bool get hasPricing => fixedPrice != null || priceFrom != null || priceTo != null;

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: json['category'] as String?,
      priceFrom: (json['price_from'] as num?)?.toDouble(),
      priceTo: (json['price_to'] as num?)?.toDouble(),
      fixedPrice: (json['fixed_price'] as num?)?.toDouble(),
      durationMinutes: json['duration_minutes'] as int?,
      imageUrl: json['image_url'] as String?,
      isAvailable: json['is_available'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'price_from': priceFrom,
      'price_to': priceTo,
      'fixed_price': fixedPrice,
      'duration_minutes': durationMinutes,
      'image_url': imageUrl,
      'is_available': isAvailable,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ServiceModel copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    double? priceFrom,
    double? priceTo,
    double? fixedPrice,
    int? durationMinutes,
    String? imageUrl,
    bool? isAvailable,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      priceFrom: priceFrom ?? this.priceFrom,
      priceTo: priceTo ?? this.priceTo,
      fixedPrice: fixedPrice ?? this.fixedPrice,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      imageUrl: imageUrl ?? this.imageUrl,
      isAvailable: isAvailable ?? this.isAvailable,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ServiceModel(id: $id, name: $name, price: $formattedPrice)';
  }
}
