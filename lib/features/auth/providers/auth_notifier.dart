import 'package:flutter/foundation.dart';
import '../data/auth_repository.dart';
import '../domain/user_model.dart';
import '../../../core/storage/secure_storage_service.dart';

class AuthNotifier extends ChangeNotifier {
  final AuthRepository _authRepository;

  bool _isLoading = false;
  String? _errorMessage;
  String? _token;
  UserModel? _user;

  AuthNotifier(this._authRepository);

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get token => _token;
  UserModel? get user => _user;
  bool get isAuthenticated => _token != null;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Try auto login on app start
  Future<void> tryAutoLogin() async {
    try {
      final savedToken = await SecureStorageService.getAuthToken();

      if (savedToken != null) {
        debugPrint('AuthNotifier: Retrieved token: $savedToken');
        _token = savedToken;
        // Try to get user info to validate token
        try {
          _user = await _authRepository.getCurrentUser();
          notifyListeners();
        } catch (e) {
          // Token is invalid, clear it
          await logout();
        }
      }
    } catch (e) {
      // Ignore errors during auto login
    }
  }

  // Login method
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final token = await _authRepository.login(email, password);
      _token = token;

      // Save token to secure storage
      await SecureStorageService.saveAuthToken(token);
      debugPrint('AuthNotifier: Token saved: $token');

      // Get user info
      try {
        _user = await _authRepository.getCurrentUser();
        // Save user data to secure storage
        if (_user != null) {
          await SecureStorageService.saveUserData(
            userId: _user!.id,
            email: _user!.email,
          );
        }
      } catch (e) {
        // Continue even if user info fails
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Register method
  Future<bool> register(String name, String email, String password) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final token = await _authRepository.register(name, email, password);
      _token = token;

      // Save token to secure storage
      await SecureStorageService.saveAuthToken(token);

      // Get user info
      try {
        _user = await _authRepository.getCurrentUser();
        // Save user data to secure storage
        if (_user != null) {
          await SecureStorageService.saveUserData(
            userId: _user!.id,
            email: _user!.email,
          );
        }
      } catch (e) {
        // Continue even if user info fails
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString().replaceFirst('Exception: ', '');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    try {
      // Call logout API if we have a token
      if (_token != null) {
        await _authRepository.logout();
      }
    } catch (e) {
      // Continue with logout even if API call fails
    }

    _token = null;
    _user = null;
    _errorMessage = null;

    // Clear all data from secure storage
    await SecureStorageService.clearAll();

    notifyListeners();
  }
}
