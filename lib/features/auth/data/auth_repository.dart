import 'package:dio/dio.dart';
import '../domain/user_model.dart';

class AuthRepository {
  final Dio _dio;

  AuthRepository(this._dio);

  Future<String> login(String email, String password) async {
    try {
      final response = await _dio.post(
        '/login',
        data: {'email': email, 'password': password},
      );

      if (response.statusCode == 200) {
        return response.data['data']['token'] as String;
      } else {
        throw Exception('Login failed');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Invalid email or password');
      } else if (e.response?.statusCode == 422) {
        throw Exception('Please check your email and password');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<String> register(String name, String email, String password) async {
    try {
      final response = await _dio.post(
        '/register',
        data: {
          'name': name,
          'email': email,
          'password': password,
          'password_confirmation': password,
        },
      );

      if (response.statusCode == 201) {
        return response.data['data']['token'] as String;
      } else {
        throw Exception('Registration failed');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        if (errors != null) {
          final firstError = errors.values.first as List;
          throw Exception(firstError.first as String);
        }
        throw Exception('Please check your information');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<UserModel> getCurrentUser() async {
    try {
      final response = await _dio.get('/me');

      if (response.statusCode == 200) {
        return UserModel.fromJson(
          response.data['data'] as Map<String, dynamic>,
        );
      } else {
        throw Exception('Failed to get user information');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Session expired. Please login again.');
      } else {
        throw Exception('Network error. Please try again.');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred');
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/logout');
    } on DioException catch (e) {
      // Continue with logout even if API call fails
      if (e.response?.statusCode != 401) {
        throw Exception('Network error during logout');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred during logout');
    }
  }
}
