import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Primary Colors
  static const Color primaryPurple = Color(0xFF7C3AED); // rgb(124, 58, 237)
  static const Color primaryDark = Color(0xFF5B21B6); // rgb(91, 33, 182)
  static const Color primaryLight = Color(0xFFEDE9FE); // rgb(237, 233, 254)

  // Secondary Colors
  static const Color successGreen = Color(0xFF10B981); // rgb(16, 185, 129)
  static const Color warningOrange = Color(0xFFF59E0B); // rgb(245, 158, 11)
  static const Color errorRed = Color(0xFFEF4444); // rgb(239, 68, 68)
  static const Color infoBlue = Color(0xFF3B82F6); // rgb(59, 130, 246)

  // Accent Colors
  static const Color goldStar = Color(0xFFFCD34D); // rgb(252, 211, 77)
  static const Color heartRed = Color(0xFFF87171); // rgb(248, 113, 113)
  static const Color distanceBlue = Color(0xFF60A5FA); // rgb(96, 165, 250)

  // Neutral Colors
  static const Color gray900 = Color(
    0xFF111827,
  ); // rgb(17, 24, 39) - Primary Text
  static const Color gray700 = Color(
    0xFF374151,
  ); // rgb(55, 65, 81) - Secondary Text
  static const Color gray500 = Color(
    0xFF6B7280,
  ); // rgb(107, 114, 128) - Placeholder Text
  static const Color gray300 = Color(
    0xFFD1D5DB,
  ); // rgb(209, 213, 219) - Borders
  static const Color gray600 = Color(
    0xFF4B5563,
  ); // rgb(75, 85, 99) - Muted Text
  static const Color gray400 = Color(
    0xFF9CA3AF,
  ); // rgb(156, 163, 175) - Disabled Text
  static const Color gray200 = Color(
    0xFFE5E7EB,
  ); // rgb(229, 231, 235) - Light Borders
  static const Color gray100 = Color(
    0xFFF3F4F6,
  ); // rgb(243, 244, 246) - Background
  static const Color white = Color(0xFFFFFFFF); // rgb(255, 255, 255)

  // Spacing System (Base Unit: 4px)
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 12.0;
  static const double lg = 16.0;
  static const double xl = 20.0;
  static const double xxl = 24.0;
  static const double xxxl = 32.0;

  // Typography Styles
  static TextStyle get h1 =>
      GoogleFonts.inter(fontSize: 28, fontWeight: FontWeight.w700, height: 1.2);

  static TextStyle get h2 =>
      GoogleFonts.inter(fontSize: 24, fontWeight: FontWeight.w700, height: 1.3);

  static TextStyle get h3 =>
      GoogleFonts.inter(fontSize: 20, fontWeight: FontWeight.w600, height: 1.4);

  static TextStyle get h4 =>
      GoogleFonts.inter(fontSize: 18, fontWeight: FontWeight.w600, height: 1.4);

  static TextStyle get h5 =>
      GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w500, height: 1.5);

  static TextStyle get bodyLarge =>
      GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w400, height: 1.6);

  static TextStyle get bodyMedium =>
      GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w400, height: 1.5);

  static TextStyle get bodySmall =>
      GoogleFonts.inter(fontSize: 12, fontWeight: FontWeight.w400, height: 1.4);

  static TextStyle get caption =>
      GoogleFonts.inter(fontSize: 10, fontWeight: FontWeight.w400, height: 1.3);

  static TextStyle get buttonText =>
      GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w600, height: 1.0);

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: primaryPurple,
        secondary: successGreen,
        surface: white,
        onSurface: gray900,
        error: errorRed,
        onError: white,
        background: gray100,
        onBackground: gray900,
      ),
      textTheme: GoogleFonts.interTextTheme().copyWith(
        headlineLarge: h1.copyWith(color: gray900),
        headlineMedium: h2.copyWith(color: gray900),
        headlineSmall: h3.copyWith(color: gray900),
        titleLarge: h4.copyWith(color: gray900),
        titleMedium: h5.copyWith(color: gray900),
        bodyLarge: bodyLarge.copyWith(color: gray900),
        bodyMedium: bodyMedium.copyWith(color: gray700),
        bodySmall: bodySmall.copyWith(color: gray500),
        labelLarge: buttonText.copyWith(color: white),
        labelMedium: caption.copyWith(color: gray500),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: white,
        foregroundColor: gray900,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: h3.copyWith(color: gray900),
        iconTheme: const IconThemeData(color: gray900),
      ),
      cardTheme: CardThemeData(
        color: white,
        elevation: 1,
        shadowColor: gray900.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(md)),
        margin: const EdgeInsets.symmetric(horizontal: lg, vertical: xs),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryPurple,
          foregroundColor: white,
          textStyle: buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(sm),
          ),
          padding: const EdgeInsets.symmetric(horizontal: xxl, vertical: md),
          elevation: 0,
          minimumSize: const Size(0, 44), // Minimum touch target
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryPurple,
          textStyle: bodyMedium.copyWith(fontWeight: FontWeight.w500),
          minimumSize: const Size(0, 44), // Minimum touch target
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(xxl),
          borderSide: const BorderSide(color: gray300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(xxl),
          borderSide: const BorderSide(color: gray300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(xxl),
          borderSide: const BorderSide(color: primaryPurple, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(xxl),
          borderSide: const BorderSide(color: errorRed),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: lg,
          vertical: md,
        ),
        labelStyle: bodyMedium.copyWith(color: gray500),
        hintStyle: bodyMedium.copyWith(color: gray500),
        filled: true,
        fillColor: white,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: white,
        selectedItemColor: primaryPurple,
        unselectedItemColor: gray500,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: gray100,
        selectedColor: primaryPurple,
        disabledColor: gray300,
        labelStyle: bodySmall.copyWith(color: gray700),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(xl),
          side: const BorderSide(color: gray300),
        ),
        selectedShadowColor: Colors.transparent,
        shadowColor: Colors.transparent,
        elevation: 0,
        pressElevation: 0,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryPurple,
        foregroundColor: white,
        elevation: 2,
      ),
    );
  }
}
