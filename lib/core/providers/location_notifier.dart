import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../services/location_service.dart';

class LocationNotifier extends ChangeNotifier {
  final LocationService _locationService = LocationService.instance;

  Position? _currentPosition;
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasPermission = false;
  bool _isLocationEnabled = false;

  // Getters
  Position? get currentPosition => _currentPosition;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasPermission => _hasPermission;
  bool get isLocationEnabled => _isLocationEnabled;
  bool get hasLocation => _currentPosition != null;

  // Get current location coordinates
  double? get latitude => _currentPosition?.latitude;
  double? get longitude => _currentPosition?.longitude;

  /// Initialize location service
  Future<void> initialize() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final isInitialized = await _locationService.initialize();
      _isLocationEnabled = _locationService.isLocationEnabled;
      _hasPermission = await _locationService.hasLocationPermission();

      if (isInitialized && _hasPermission) {
        // Try to get last known position first for faster response
        final lastKnown = await _locationService.getLastKnownPosition();
        if (lastKnown != null) {
          _currentPosition = lastKnown;
          notifyListeners();
        }

        // Then get current position for accuracy
        await getCurrentLocation();
      } else {
        _errorMessage = _getLocationErrorMessage();
      }
    } catch (e) {
      _errorMessage = 'Failed to initialize location service';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Get current location
  Future<bool> getCurrentLocation() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final position = await _locationService.getCurrentPosition();
      
      if (position != null) {
        _currentPosition = position;
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _errorMessage = 'Unable to get current location';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Error getting location: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Request location permission
  Future<bool> requestPermission() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final granted = await _locationService.requestLocationPermission();
      _hasPermission = granted;

      if (granted) {
        await getCurrentLocation();
      } else {
        _errorMessage = 'Location permission denied';
      }

      _isLoading = false;
      notifyListeners();
      return granted;
    } catch (e) {
      _errorMessage = 'Error requesting permission: ${e.toString()}';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// Open location settings
  Future<void> openLocationSettings() async {
    await _locationService.openLocationSettings();
  }

  /// Open app settings
  Future<void> openAppSettings() async {
    await _locationService.openAppSettings();
  }

  /// Calculate distance to a point
  double? calculateDistanceTo({
    required double latitude,
    required double longitude,
  }) {
    if (_currentPosition == null) return null;

    return _locationService.calculateDistance(
      startLatitude: _currentPosition!.latitude,
      startLongitude: _currentPosition!.longitude,
      endLatitude: latitude,
      endLongitude: longitude,
    );
  }

  /// Format distance for display
  String formatDistance(double distanceKm) {
    return _locationService.formatDistance(distanceKm);
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Reset location data
  void reset() {
    _currentPosition = null;
    _isLoading = false;
    _errorMessage = null;
    _hasPermission = false;
    _isLocationEnabled = false;
    notifyListeners();
  }

  String _getLocationErrorMessage() {
    if (!_isLocationEnabled) {
      return 'Location services are disabled. Please enable them in settings.';
    } else if (!_hasPermission) {
      return 'Location permission is required to find nearby businesses.';
    } else {
      return 'Unable to determine location';
    }
  }

  /// Get permission status text
  String getPermissionStatusText() {
    return _locationService.getPermissionStatusText();
  }
}
