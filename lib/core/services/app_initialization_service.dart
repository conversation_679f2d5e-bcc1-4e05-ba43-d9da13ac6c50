import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import 'location_service.dart';

class AppInitializationService {
  static AppInitializationService? _instance;
  static AppInitializationService get instance => _instance ??= AppInitializationService._();
  AppInitializationService._();

  bool _isInitialized = false;
  bool _onboardingCompleted = false;
  bool _hasLocationPermission = false;

  bool get isInitialized => _isInitialized;
  bool get onboardingCompleted => _onboardingCompleted;
  bool get hasLocationPermission => _hasLocationPermission;

  /// Initialize the app and check various states
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check onboarding status
      await _checkOnboardingStatus();
      
      // Check location permission status
      await _checkLocationPermissionStatus();
      
      // Initialize other services
      await _initializeOtherServices();
      
      _isInitialized = true;
      debugPrint('App initialization completed');
    } catch (e) {
      debugPrint('App initialization failed: $e');
      // Continue with app even if initialization fails
      _isInitialized = true;
    }
  }

  /// Check if user has completed onboarding
  Future<void> _checkOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;
      debugPrint('Onboarding completed: $_onboardingCompleted');
    } catch (e) {
      debugPrint('Failed to check onboarding status: $e');
      _onboardingCompleted = false;
    }
  }

  /// Check location permission status
  Future<void> _checkLocationPermissionStatus() async {
    try {
      final locationService = LocationService.instance;
      _hasLocationPermission = await locationService.checkLocationStatus();
      debugPrint('Location permission granted: $_hasLocationPermission');
    } catch (e) {
      debugPrint('Failed to check location permission: $e');
      _hasLocationPermission = false;
    }
  }

  /// Initialize other services
  Future<void> _initializeOtherServices() async {
    // Add other service initializations here
    // For example: analytics, crash reporting, etc.
  }

  /// Mark onboarding as completed
  Future<void> completeOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed', true);
      _onboardingCompleted = true;
      debugPrint('Onboarding marked as completed');
    } catch (e) {
      debugPrint('Failed to mark onboarding as completed: $e');
    }
  }

  /// Update location permission status
  void updateLocationPermissionStatus(bool granted) {
    _hasLocationPermission = granted;
    debugPrint('Location permission updated: $granted');
  }

  /// Get the initial route based on app state
  String getInitialRoute() {
    if (!_onboardingCompleted) {
      return '/onboarding';
    }
    
    // If onboarding is completed but no location permission,
    // we can still go to home and show location prompt later
    return '/home';
  }

  /// Check if location permission should be requested
  bool shouldRequestLocationPermission() {
    return _onboardingCompleted && !_hasLocationPermission;
  }

  /// Reset app state (for testing or logout)
  Future<void> reset() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      _isInitialized = false;
      _onboardingCompleted = false;
      _hasLocationPermission = false;
      
      debugPrint('App state reset');
    } catch (e) {
      debugPrint('Failed to reset app state: $e');
    }
  }

  /// Get app version info
  Future<Map<String, String>> getAppInfo() async {
    // This would typically use package_info_plus
    return {
      'version': '1.0.0',
      'buildNumber': '1',
    };
  }

  /// Check if this is the first app launch
  Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = !prefs.containsKey('first_launch_completed');
      
      if (isFirstLaunch) {
        await prefs.setBool('first_launch_completed', true);
      }
      
      return isFirstLaunch;
    } catch (e) {
      debugPrint('Failed to check first launch: $e');
      return false;
    }
  }

  /// Save user preferences
  Future<void> saveUserPreference(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (value is bool) {
        await prefs.setBool(key, value);
      } else if (value is int) {
        await prefs.setInt(key, value);
      } else if (value is double) {
        await prefs.setDouble(key, value);
      } else if (value is String) {
        await prefs.setString(key, value);
      } else if (value is List<String>) {
        await prefs.setStringList(key, value);
      }
      
      debugPrint('Saved user preference: $key = $value');
    } catch (e) {
      debugPrint('Failed to save user preference: $e');
    }
  }

  /// Get user preference
  Future<T?> getUserPreference<T>(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.get(key) as T?;
    } catch (e) {
      debugPrint('Failed to get user preference: $e');
      return null;
    }
  }

  /// Check if user has seen a specific feature introduction
  Future<bool> hasSeenFeatureIntro(String featureName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('feature_intro_$featureName') ?? false;
    } catch (e) {
      debugPrint('Failed to check feature intro: $e');
      return false;
    }
  }

  /// Mark feature introduction as seen
  Future<void> markFeatureIntroAsSeen(String featureName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('feature_intro_$featureName', true);
      debugPrint('Marked feature intro as seen: $featureName');
    } catch (e) {
      debugPrint('Failed to mark feature intro as seen: $e');
    }
  }
}
