import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../features/auth/providers/auth_notifier.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/discovery/screens/home_screen.dart';
import '../../features/discovery/screens/map_screen.dart';
import '../../features/search/screens/enhanced_search_screen.dart';
import '../../features/profile/screens/enhanced_profile_screen.dart';
import '../../features/profile/screens/enhanced_favorites_screen.dart';
import '../../features/business/screens/enhanced_business_detail_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/onboarding/screens/location_permission_screen.dart';
import '../../core/services/app_initialization_service.dart';
import '../../shared/widgets/main_shell.dart';

class AppRouter {
  static String _getInitialLocation() {
    final appInitService = AppInitializationService.instance;
    if (appInitService.isInitialized) {
      return appInitService.getInitialRoute();
    }
    // Default to login if not initialized
    return '/login';
  }

  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: _getInitialLocation(),
      redirect: (context, state) {
        final authNotifier = context.read<AuthNotifier>();
        final isLoggedIn = authNotifier.token != null;
        final isLoggingIn =
            state.matchedLocation == '/login' ||
            state.matchedLocation == '/register';

        // If not logged in and trying to access protected routes, redirect to login
        if (!isLoggedIn && !isLoggingIn) {
          return '/login';
        }

        // If logged in and trying to access auth routes, redirect to home
        if (isLoggedIn && isLoggingIn) {
          return '/home';
        }

        return null; // No redirect needed
      },
      routes: [
        // Auth routes (outside shell)
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          builder: (context, state) => const RegisterScreen(),
        ),

        // Onboarding routes
        GoRoute(
          path: '/onboarding',
          builder: (context, state) => const OnboardingScreen(),
        ),
        GoRoute(
          path: '/location-permission',
          builder: (context, state) => LocationPermissionScreen(
            onPermissionGranted: () => context.go('/home'),
            onSkip: () => context.go('/home'),
          ),
        ),

        // Business detail route (outside shell)
        GoRoute(
          path: '/business/:slug',
          builder: (context, state) {
            final slug = state.pathParameters['slug']!;
            return EnhancedBusinessDetailScreen(slug: slug);
          },
        ),

        // Main shell with bottom navigation
        ShellRoute(
          builder: (context, state, child) => MainShell(child: child),
          routes: [
            GoRoute(
              path: '/home',
              builder: (context, state) => const HomeScreen(),
            ),
            GoRoute(
              path: '/search',
              builder: (context, state) {
                final query = state.uri.queryParameters['query'];
                final categoryId = state.uri.queryParameters['category'];
                return EnhancedSearchScreen(
                  initialQuery: query,
                  categoryId: categoryId != null
                      ? int.tryParse(categoryId)
                      : null,
                );
              },
            ),
            GoRoute(
              path: '/map',
              builder: (context, state) => const MapScreen(),
            ),
            GoRoute(
              path: '/favorites',
              builder: (context, state) => const EnhancedFavoritesScreen(),
            ),
            GoRoute(
              path: '/profile',
              builder: (context, state) => const EnhancedProfileScreen(),
            ),
          ],
        ),
      ],
    );
  }
}
