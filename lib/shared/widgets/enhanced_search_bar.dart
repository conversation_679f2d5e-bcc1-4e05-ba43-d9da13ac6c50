import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../core/theme/app_theme.dart';

class EnhancedSearchBar extends StatefulWidget {
  final String? hintText;
  final String? initialValue;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onFilterTap;
  final bool showFilterButton;
  final int activeFiltersCount;
  final bool enabled;
  final bool autofocus;

  const EnhancedSearchBar({
    super.key,
    this.hintText = 'Search businesses...',
    this.initialValue,
    this.onChanged,
    this.onSubmitted,
    this.onFilterTap,
    this.showFilterButton = true,
    this.activeFiltersCount = 0,
    this.enabled = true,
    this.autofocus = false,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.lg),
      child: Row(
        children: [
          // Search Input Field
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(AppTheme.xxl),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.gray900.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: widget.enabled,
                onChanged: widget.onChanged,
                onSubmitted: widget.onSubmitted,
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                  prefixIcon: const Icon(
                    FeatherIcons.search,
                    color: AppTheme.gray500,
                    size: 20,
                  ),
                  suffixIcon: _controller.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(
                            FeatherIcons.x,
                            color: AppTheme.gray500,
                            size: 20,
                          ),
                          onPressed: () {
                            _controller.clear();
                            widget.onChanged?.call('');
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.xxl),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.xxl),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.xxl),
                    borderSide: const BorderSide(
                      color: AppTheme.primaryPurple,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.lg,
                    vertical: AppTheme.md,
                  ),
                  filled: true,
                  fillColor: AppTheme.white,
                ),
              ),
            ),
          ),
          
          // Filter Button
          if (widget.showFilterButton) ...[
            const SizedBox(width: AppTheme.md),
            _buildFilterButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterButton() {
    final hasActiveFilters = widget.activeFiltersCount > 0;
    
    return GestureDetector(
      onTap: widget.onFilterTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.md),
        decoration: BoxDecoration(
          color: hasActiveFilters ? AppTheme.primaryPurple : AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.md),
          boxShadow: [
            BoxShadow(
              color: AppTheme.gray900.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Icon(
              FeatherIcons.sliders,
              color: hasActiveFilters ? AppTheme.white : AppTheme.gray700,
              size: 20,
            ),
            // Filter Count Badge
            if (hasActiveFilters)
              Positioned(
                top: -6,
                right: -6,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: AppTheme.errorRed,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    widget.activeFiltersCount.toString(),
                    style: AppTheme.caption.copyWith(
                      color: AppTheme.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Simple search bar for read-only display (tappable to navigate to search)
class SearchBarPlaceholder extends StatelessWidget {
  final String hintText;
  final VoidCallback? onTap;

  const SearchBarPlaceholder({
    super.key,
    this.hintText = 'Search businesses...',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(AppTheme.lg),
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.lg,
          vertical: AppTheme.md,
        ),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.xxl),
          border: Border.all(color: AppTheme.gray300),
          boxShadow: [
            BoxShadow(
              color: AppTheme.gray900.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(
              FeatherIcons.search,
              color: AppTheme.gray500,
              size: 20,
            ),
            const SizedBox(width: AppTheme.md),
            Text(
              hintText,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
            ),
          ],
        ),
      ),
    );
  }
}
