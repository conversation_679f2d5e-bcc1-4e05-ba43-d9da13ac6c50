import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../core/providers/location_notifier.dart';

class LocationPermissionDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onPermissionGranted;

  const LocationPermissionDialog({
    super.key,
    this.title = 'Location Permission Required',
    this.message = 'This app needs location access to find nearby businesses and provide personalized recommendations.',
    this.onPermissionGranted,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocationNotifier>(
      builder: (context, locationNotifier, child) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                FeatherIcons.mapPin,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[700],
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              
              // Permission benefits
              _buildBenefitItem(
                icon: FeatherIcons.search,
                text: 'Find businesses near you',
              ),
              const SizedBox(height: 8),
              _buildBenefitItem(
                icon: FeatherIcons.navigation,
                text: 'Get accurate directions',
              ),
              const SizedBox(height: 8),
              _buildBenefitItem(
                icon: FeatherIcons.star,
                text: 'Personalized recommendations',
              ),
              
              if (locationNotifier.errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        FeatherIcons.alertCircle,
                        color: Colors.red[600],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          locationNotifier.errorMessage!,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.red[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: locationNotifier.isLoading ? null : () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Not Now',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: locationNotifier.isLoading ? null : () async {
                final granted = await locationNotifier.requestPermission();
                
                if (granted) {
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    onPermissionGranted?.call();
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: locationNotifier.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Allow Location',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.green[600],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  static Future<void> show(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onPermissionGranted,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => LocationPermissionDialog(
        title: title ?? 'Location Permission Required',
        message: message ?? 'This app needs location access to find nearby businesses and provide personalized recommendations.',
        onPermissionGranted: onPermissionGranted,
      ),
    );
  }
}
