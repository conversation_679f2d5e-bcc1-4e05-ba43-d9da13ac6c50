import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:provider/provider.dart';

import '../../features/business/domain/business_summary_model.dart';
import '../../features/profile/providers/favorites_notifier.dart';
import '../../core/theme/app_theme.dart';

class EnhancedBusinessCard extends StatelessWidget {
  final BusinessSummaryModel business;

  const EnhancedBusinessCard({super.key, required this.business});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.md),
        boxShadow: [
          BoxShadow(
            color: AppTheme.gray900.withOpacity(0.1),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => context.go('/business/${business.slug}'),
          borderRadius: BorderRadius.circular(AppTheme.md),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.md),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Business Logo/Image
                _buildBusinessImage(),
                const SizedBox(width: AppTheme.md),
                // Business Info
                Expanded(child: _buildBusinessInfo(context)),
                // Favorite Button
                _buildFavoriteButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppTheme.sm),
      child: SizedBox(
        width: 80,
        height: 80,
        child: business.logoUrl != null
            ? CachedNetworkImage(
                imageUrl: business.logoUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppTheme.gray100,
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppTheme.primaryPurple,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppTheme.gray100,
                  child: const Icon(
                    FeatherIcons.briefcase,
                    color: AppTheme.gray500,
                    size: 32,
                  ),
                ),
              )
            : Container(
                color: AppTheme.gray100,
                child: const Icon(
                  FeatherIcons.briefcase,
                  color: AppTheme.gray500,
                  size: 32,
                ),
              ),
      ),
    );
  }

  Widget _buildBusinessInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Business Name
        Text(
          business.name,
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: AppTheme.xs),
        // Rating Row
        _buildRatingRow(),
        const SizedBox(height: AppTheme.xs),
        // Address
        _buildAddressRow(),
        const SizedBox(height: AppTheme.xs),
        // Status Row (Distance + Open/Closed)
        _buildStatusRow(),
      ],
    );
  }

  Widget _buildRatingRow() {
    return Row(
      children: [
        RatingBarIndicator(
          rating: business.averageRating,
          itemBuilder: (context, index) => const Icon(
            Icons.star,
            color: AppTheme.goldStar,
          ),
          itemCount: 5,
          itemSize: 14,
        ),
        const SizedBox(width: AppTheme.xs),
        Text(
          '${business.averageRating.toStringAsFixed(1)}',
          style: AppTheme.bodySmall.copyWith(color: AppTheme.gray700),
        ),
        const SizedBox(width: AppTheme.xs),
        Text(
          '(${business.reviewCount})',
          style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
        ),
        const SizedBox(width: AppTheme.sm),
        Text(
          business.priceRange,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.gray700,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressRow() {
    return Row(
      children: [
        const Icon(
          FeatherIcons.mapPin,
          size: 12,
          color: AppTheme.gray500,
        ),
        const SizedBox(width: AppTheme.xs),
        Expanded(
          child: Text(
            business.address,
            style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusRow() {
    return Row(
      children: [
        // Distance
        if (business.distanceKm != null) ...[
          const Icon(
            FeatherIcons.navigation,
            size: 12,
            color: AppTheme.distanceBlue,
          ),
          const SizedBox(width: AppTheme.xs),
          Text(
            '${business.distanceKm!.toStringAsFixed(1)} km',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.distanceBlue,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: AppTheme.sm),
        ],
        // Open/Closed Status
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.sm,
            vertical: 2,
          ),
          decoration: BoxDecoration(
            color: business.isOpenNow ? AppTheme.successGreen : AppTheme.errorRed,
            borderRadius: BorderRadius.circular(AppTheme.xs),
          ),
          child: Text(
            business.isOpenNow ? 'Open' : 'Closed',
            style: AppTheme.caption.copyWith(
              color: AppTheme.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFavoriteButton(BuildContext context) {
    return Consumer<FavoritesNotifier>(
      builder: (context, favoritesNotifier, child) {
        final isFavorite = favoritesNotifier.isFavorite(business.id);
        
        return GestureDetector(
          onTap: () => favoritesNotifier.toggleFavorite(business.id),
          child: Container(
            padding: const EdgeInsets.all(AppTheme.xs),
            child: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? AppTheme.heartRed : AppTheme.gray500,
              size: 24,
            ),
          ),
        );
      },
    );
  }
}
