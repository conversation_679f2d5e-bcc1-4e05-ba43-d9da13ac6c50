import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../features/discovery/domain/category_model.dart';
import '../../core/theme/app_theme.dart';

class CategoryChip extends StatelessWidget {
  final CategoryModel category;
  final bool isSelected;
  final VoidCallback? onTap;
  final int? count; // Optional count badge

  const CategoryChip({
    super.key,
    required this.category,
    this.isSelected = false,
    this.onTap,
    this.count,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: AppTheme.sm),
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.lg,
          vertical: AppTheme.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryPurple : AppTheme.gray100,
          borderRadius: BorderRadius.circular(AppTheme.xl),
          border: Border.all(
            color: isSelected ? AppTheme.primaryPurple : AppTheme.gray300,
            width: 1,
          ),
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Category Icon
                _buildCategoryIcon(),
                const SizedBox(width: AppTheme.xs),
                // Category Name
                Text(
                  category.name,
                  style: AppTheme.bodySmall.copyWith(
                    color: isSelected ? AppTheme.white : AppTheme.gray700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            // Count Badge
            if (count != null && count! > 0)
              Positioned(
                top: -8,
                right: -8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: AppTheme.primaryPurple,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    count.toString(),
                    style: AppTheme.caption.copyWith(
                      color: AppTheme.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryIcon() {
    return SizedBox(
      width: 16,
      height: 16,
      child: category.iconUrl != null
          ? CachedNetworkImage(
              imageUrl: category.iconUrl!,
              width: 16,
              height: 16,
              fit: BoxFit.contain,
              placeholder: (context, url) => const Icon(
                FeatherIcons.grid,
                size: 16,
                color: AppTheme.gray500,
              ),
              errorWidget: (context, url, error) => const Icon(
                FeatherIcons.grid,
                size: 16,
                color: AppTheme.gray500,
              ),
              color: isSelected ? AppTheme.white : AppTheme.gray700,
            )
          : Icon(
              FeatherIcons.grid,
              size: 16,
              color: isSelected ? AppTheme.white : AppTheme.gray700,
            ),
    );
  }
}

/// Horizontal scrollable list of category chips
class CategoryChipList extends StatelessWidget {
  final List<CategoryModel> categories;
  final List<int> selectedCategoryIds;
  final Function(int categoryId) onCategoryTap;
  final Map<int, int>? categoryCounts; // Optional counts for each category

  const CategoryChipList({
    super.key,
    required this.categories,
    required this.selectedCategoryIds,
    required this.onCategoryTap,
    this.categoryCounts,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategoryIds.contains(category.id);
          final count = categoryCounts?[category.id];

          return CategoryChip(
            category: category,
            isSelected: isSelected,
            count: count,
            onTap: () => onCategoryTap(category.id),
          );
        },
      ),
    );
  }
}
