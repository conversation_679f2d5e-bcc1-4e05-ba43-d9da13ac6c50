import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import '../../features/business/domain/business_summary_model.dart';
import '../../core/theme/app_theme.dart';

class BusinessSummaryCard extends StatelessWidget {
  final BusinessSummaryModel business;

  const BusinessSummaryCard({super.key, required this.business});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => context.go('/business/${business.slug}'),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Business Logo/Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: SizedBox(
                  width: 80,
                  height: 80,
                  child: business.logoUrl != null
                      ? CachedNetworkImage(
                          imageUrl: business.logoUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[300],
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.business,
                              color: Colors.grey[600],
                              size: 32,
                            ),
                          ),
                        )
                      : Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.business,
                            color: Colors.grey[600],
                            size: 32,
                          ),
                        ),
                ),
              ),

              const SizedBox(width: 12),

              // Business Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Business Name
                    Text(
                      business.name,
                      style: AppTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.gray900,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // Rating and Review Count
                    Row(
                      children: [
                        RatingBarIndicator(
                          rating: business.averageRating,
                          itemBuilder: (context, index) =>
                              const Icon(Icons.star, color: Colors.amber),
                          itemCount: 5,
                          itemSize: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '${business.averageRating.toStringAsFixed(1)} (${business.reviewCount})',
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.gray600,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 6),

                    // Address
                    Row(
                      children: [
                        Icon(
                          FeatherIcons.mapPin,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            business.address,
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.gray600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    // Distance (if available)
                    if (business.distanceKm != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            FeatherIcons.navigation,
                            size: 12,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${business.distanceKm!.toStringAsFixed(1)} km away',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.primaryPurple,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Arrow Icon
              Icon(Icons.chevron_right, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }
}
