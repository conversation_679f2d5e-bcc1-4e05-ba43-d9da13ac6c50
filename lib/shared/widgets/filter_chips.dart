import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../../core/theme/app_theme.dart';

class FilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback? onTap;
  final IconData? icon;
  final Color? selectedColor;
  final bool showCloseButton;

  const FilterChip({
    super.key,
    required this.label,
    this.isSelected = false,
    this.onTap,
    this.icon,
    this.selectedColor,
    this.showCloseButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveSelectedColor = selectedColor ?? AppTheme.primaryPurple;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: AppTheme.sm),
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.md,
          vertical: AppTheme.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? effectiveSelectedColor : AppTheme.gray100,
          borderRadius: BorderRadius.circular(AppTheme.xl),
          border: Border.all(
            color: isSelected ? effectiveSelectedColor : AppTheme.gray300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 14,
                color: isSelected ? AppTheme.white : AppTheme.gray700,
              ),
              const SizedBox(width: AppTheme.xs),
            ],
            Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: isSelected ? AppTheme.white : AppTheme.gray700,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (showCloseButton && isSelected) ...[
              const SizedBox(width: AppTheme.xs),
              Icon(FeatherIcons.x, size: 14, color: AppTheme.white),
            ],
          ],
        ),
      ),
    );
  }
}

class FilterChipList extends StatelessWidget {
  final List<FilterChipData> filters;
  final Function(String filterId) onFilterTap;
  final ScrollController? scrollController;

  const FilterChipList({
    super.key,
    required this.filters,
    required this.onFilterTap,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 36,
      child: ListView.builder(
        controller: scrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.lg),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          return FilterChip(
            label: filter.label,
            isSelected: filter.isSelected,
            icon: filter.icon,
            selectedColor: filter.selectedColor,
            showCloseButton: filter.showCloseButton,
            onTap: () => onFilterTap(filter.id),
          );
        },
      ),
    );
  }
}

class FilterChipData {
  final String id;
  final String label;
  final bool isSelected;
  final IconData? icon;
  final Color? selectedColor;
  final bool showCloseButton;

  const FilterChipData({
    required this.id,
    required this.label,
    this.isSelected = false,
    this.icon,
    this.selectedColor,
    this.showCloseButton = false,
  });

  FilterChipData copyWith({
    String? id,
    String? label,
    bool? isSelected,
    IconData? icon,
    Color? selectedColor,
    bool? showCloseButton,
  }) {
    return FilterChipData(
      id: id ?? this.id,
      label: label ?? this.label,
      isSelected: isSelected ?? this.isSelected,
      icon: icon ?? this.icon,
      selectedColor: selectedColor ?? this.selectedColor,
      showCloseButton: showCloseButton ?? this.showCloseButton,
    );
  }
}

/// Predefined filter chips for common use cases
class CommonFilterChips {
  static FilterChipData sortByDistance() => const FilterChipData(
    id: 'sort_distance',
    label: 'Distance',
    icon: FeatherIcons.navigation,
  );

  static FilterChipData sortByRating() => const FilterChipData(
    id: 'sort_rating',
    label: 'Rating',
    icon: FeatherIcons.star,
  );

  static FilterChipData sortByPrice() => const FilterChipData(
    id: 'sort_price',
    label: 'Price',
    icon: FeatherIcons.dollarSign,
  );

  static FilterChipData openNow() => const FilterChipData(
    id: 'open_now',
    label: 'Open Now',
    icon: FeatherIcons.clock,
    selectedColor: AppTheme.successGreen,
  );

  static FilterChipData hasProducts() => const FilterChipData(
    id: 'has_products',
    label: 'Has Products',
    icon: FeatherIcons.package,
  );

  static FilterChipData hasServices() => const FilterChipData(
    id: 'has_services',
    label: 'Has Services',
    icon: FeatherIcons.settings,
  );

  static FilterChipData priceRange(String range) => FilterChipData(
    id: 'price_$range',
    label: range,
    icon: FeatherIcons.dollarSign,
  );

  static FilterChipData rating(double minRating) => FilterChipData(
    id: 'rating_$minRating',
    label: '$minRating+ Stars',
    icon: FeatherIcons.star,
    selectedColor: AppTheme.goldStar,
  );

  static FilterChipData distance(int km) => FilterChipData(
    id: 'distance_$km',
    label: 'Within ${km}km',
    icon: FeatherIcons.mapPin,
    selectedColor: AppTheme.distanceBlue,
  );
}
