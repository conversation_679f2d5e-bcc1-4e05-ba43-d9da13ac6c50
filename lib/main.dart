import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';

import 'core/theme/app_theme.dart';
import 'core/api/api_client.dart';
import 'core/router/app_router.dart';
import 'features/auth/providers/auth_notifier.dart';
import 'features/auth/data/auth_repository.dart';
import 'features/search/providers/search_notifier.dart';
import 'features/profile/providers/favorites_notifier.dart';
import 'features/business/data/business_repository.dart';
import 'features/business/data/review_repository.dart';
import 'features/business/data/media_repository.dart';
import 'features/business/providers/review_notifier.dart';
import 'features/business/providers/media_notifier.dart';
import 'features/profile/data/user_repository.dart';
import 'core/providers/location_notifier.dart';
import 'core/services/app_initialization_service.dart';
import 'shared/widgets/app_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const LocalFindApp());
}

class LocalFindApp extends StatelessWidget {
  const LocalFindApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 1. Base Dio instance
        Provider<Dio>(create: (context) => ApiClient.createDio()),

        // 2. Auth Notifier (initial, will be overridden by the ChangeNotifierProxyProvider later)
        //    This is needed so that ProxyProvider<AuthNotifier, Dio> has AuthNotifier to depend on.
        ChangeNotifierProvider<AuthNotifier>(
          create: (context) => AuthNotifier(AuthRepository(context.read<Dio>())),
        ),

        // 3. Authenticated Dio instance (depends on AuthNotifier's token)
        ProxyProvider<AuthNotifier, Dio>(
          update: (context, authNotifier, previousDio) {
            if (authNotifier.isAuthenticated) {
              return ApiClient.createAuthenticatedDio(authNotifier.token);
            }
            return context.read<Dio>(); // Fallback to base Dio if not authenticated
          },
        ),

        // 4. AuthRepository (depends on Dio)
        //    This AuthRepository will update to use the authenticated Dio when it becomes available.
        ProxyProvider<Dio, AuthRepository>(
          update: (context, dio, _) => AuthRepository(dio),
        ),

        // 5. Final AuthNotifier (depends on AuthRepository)
        //    This replaces the initial AuthNotifier and ensures it uses the dynamically updated AuthRepository.
        ChangeNotifierProxyProvider<AuthRepository, AuthNotifier>(
          create: (context) => AuthNotifier(context.read<AuthRepository>()),
          update: (context, authRepository, authNotifier) {
            // This is crucial: recreate AuthNotifier with the updated AuthRepository.
            // Since _authRepository is final, we must return a new instance.
            return AuthNotifier(authRepository);
          },
        ),

        // Repositories (now correctly depend on the dynamically updated Dio)
        ProxyProvider<Dio, BusinessRepository>(
          update: (context, dio, _) => BusinessRepository(dio),
        ),

        ProxyProvider<Dio, ReviewRepository>(
          update: (context, dio, _) => ReviewRepository(dio),
        ),

        ProxyProvider<Dio, MediaRepository>(
          update: (context, dio, _) => MediaRepository(dio),
        ),

        ProxyProvider<Dio, UserRepository>(
          update: (context, dio, _) => UserRepository(dio),
        ),

        // Other Notifiers
        ChangeNotifierProvider<SearchNotifier>(
          create: (context) =>
              SearchNotifier(context.read<BusinessRepository>()),
        ),

        ChangeNotifierProvider<ReviewNotifier>(
          create: (context) => ReviewNotifier(context.read<ReviewRepository>()),
        ),

        ChangeNotifierProvider<MediaNotifier>(
          create: (context) => MediaNotifier(context.read<MediaRepository>()),
        ),

        ChangeNotifierProvider<FavoritesNotifier>(
          create: (context) =>
              FavoritesNotifier(context.read<UserRepository>()),
        ),

        ChangeNotifierProvider<LocationNotifier>(
          create: (context) => LocationNotifier(),
        ),
      ],
      child: AppInitializer(
        child: Consumer<AuthNotifier>(
          builder: (context, authNotifier, child) {
            return MaterialApp.router(
              title: 'LocalFind',
              theme: AppTheme.lightTheme,
              routerConfig: AppRouter.createRouter(),
              debugShowCheckedModeBanner: false,
            );
          },
        ),
      ),
    );
  }
}
